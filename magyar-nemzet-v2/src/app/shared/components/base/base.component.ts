import { AsyncPipe, DOCUMENT, NgIf } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Inject,
  OnDestroy,
  OnInit,
  Optional,
  signal,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, NavigationEnd, NavigationStart, Router, RouterOutlet } from '@angular/router';
import { StorageService, UtilService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ALL_BANNER_LIST,
  AnalyticsService,
  BreakingBlock,
  ChampionshipSchedule,
  EBPortalEnum,
  ElectionsBoxComponent,
  ElectionsBoxStyle,
  InitResolverData,
  KesmaEbMatchesComponent,
  loadScript,
  MediaworksFooterCompactComponent,
  NEWSLETTER_COMPONENT_TYPE,
  OlimpiaHeaderComponent,
  OlimpiaHeaderData,
  OlimpicPortalEnum,
  PAGE_TYPES,
  PortfolioItem,
  PortfolioResponse,
  SecondaryFilterAdvertType,
  SimplifiedMenuItem,
  Sponsorship,
  TrendingTag,
} from '@trendency/kesma-ui';
import { combineLatest, fromEvent, merge, Observable, of, Subject, switchMap, take } from 'rxjs';
import { debounceTime, delay, filter, map, mergeMap, startWith, takeUntil, tap } from 'rxjs/operators';
import { isMobileApp } from 'src/app/shared/utils/device.utils';
import { FormatDate } from '../../pipes';
import { ApiService, ConfigService, EbService, ElectionsService, HeaderService, OlimpiaService, SportResultService } from '../../services';
import { Cpac2025BannerComponent } from '../cpac-2025-banner/cpac-2025-banner.component';
import { FooterComponent } from '../footer/footer.component';
import { HeaderComponent } from '../header/header.component';

declare let __tcfapi: (command: string, version?: number, callback?: (response: any, success: boolean) => void, param?: any) => void;

const BREAKING_ALLLOWED_KEY = 'breaking-allowed';

@Component({
  selector: 'app-base',
  templateUrl: './base.component.html',
  styleUrls: ['./base.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [FormatDate],
  imports: [
    NgIf,
    AdvertisementAdoceanComponent,
    HeaderComponent,
    RouterOutlet,
    FooterComponent,
    AsyncPipe,
    MediaworksFooterCompactComponent,
    ElectionsBoxComponent,
    KesmaEbMatchesComponent,
    OlimpiaHeaderComponent,
    Cpac2025BannerComponent,
  ],
})
export class BaseComponent implements OnInit, OnDestroy, AfterViewInit {
  mainMenu: SimplifiedMenuItem[] = [];
  footer: SimplifiedMenuItem[] = [];
  tags: TrendingTag[] = [];

  adverts?: AdvertisementsByMedium;
  breakingNews?: BreakingBlock;
  isArticleUrl: boolean;
  url: string;
  categorySlug: string;
  isHomePage?: boolean;

  isAdblockerActive: boolean;
  areAdsInitiated = false;

  mediaworksFooter: PortfolioItem[];

  isOlimpiaMainOrArticlePage$ = this.olimpiaService.getIsOlimpiaMainOrArticlePage$();

  isFullWidth$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => this.route.snapshot.firstChild?.data?.['isFullWidth'] === true)
  );
  facebookLink = 'https://www.facebook.com/magyarnemzet.hu';
  tiktokLink = 'https://www.tiktok.com/@magyarnemzet.napilap';
  twitterLink = 'https://twitter.com/MagyarNemzetOn';
  videaLink = 'https://videa.hu/csatornak/magyar-nemzet-404';
  instagramLink = 'https://www.instagram.com/magyarnemzet.hu/?hl=en';
  youtubeLink = 'https://www.youtube.com/@magyarnemzet_hivatalos';
  breakingAllowed = true;
  headerStuck = signal(false);
  isMobile$?: Observable<boolean>;
  sponsorship$: Observable<{ sponsor: Sponsorship; title: string } | undefined>;
  isMobileApp = true;

  ebLiveHeaderData$: Observable<ChampionshipSchedule[]> = this.sportResultService.getScheduleByCompetition(this.ebService.getSlug()).pipe(
    map((data) => data?.data?.schedules),
    tap(() => this.detectHeaderSizes())
  );

  olimpiaHeaderData$: Observable<OlimpiaHeaderData> = this.olimpiaService.getHeadlineMedalsAndEvents().pipe(
    map((data) => data?.data),
    tap(() => this.detectHeaderSizes())
  );

  EBPortalEnum = EBPortalEnum;
  OlimpicPortalEnum = OlimpicPortalEnum;
  ElectionsBoxStyle = ElectionsBoxStyle;

  @ViewChild('header', { read: ElementRef, static: false }) headerElement: ElementRef<HTMLDivElement>;
  private readonly unsubscribe$: Subject<boolean> = new Subject();
  readonly isHome$ = this.router.events.pipe(
    startWith(new NavigationEnd(0, '/', '/')),
    filter((event) => event instanceof NavigationEnd),
    map(() => this.router.url === '/' || this.router.url.includes('valasztas-2024-')),
    takeUntil(this.unsubscribe$)
  );
  private stickyObserver?: IntersectionObserver;

  readonly isCpac2025$ = this.router.events.pipe(
    filter((event) => event instanceof NavigationEnd),
    startWith(null),
    switchMap(() => combineLatest([...this.route.children.map((r) => r.params)])), //Slug to check is in child route
    map((paramsArray) => paramsArray.some((params) => params['slug'] && params['slug'] === 'cpac-hungary-2025')),
    takeUntilDestroyed()
  );

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly utils: UtilService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly apiService: ApiService,
    private readonly analyticsService: AnalyticsService,
    private readonly storage: StorageService,
    private readonly sportResultService: SportResultService,
    private readonly headerService: HeaderService,
    public readonly ebService: EbService,
    public readonly olimpiaService: OlimpiaService,
    public readonly config: ConfigService,
    public readonly electionsService: ElectionsService,
    @Inject(DOCUMENT) private readonly document: Document,
    @Optional() @Inject('REQUEST') private readonly request: Request
  ) {
    if (this.utils.isBrowser()) {
      this.isMobileApp = isMobileApp(navigator.userAgent || navigator.vendor || (window as any).opera);
    } else {
      let userAgent: string | undefined = '';
      if (this.request?.headers) {
        userAgent = Object.entries(this.request.headers).find((value) => value?.[0] === 'user-agent')?.[1];
      }
      //SSR: use request headers.
      this.isMobileApp = isMobileApp(userAgent || '');
    }
  }

  ngOnInit(): void {
    if (this.isMobileApp) {
      return;
    }

    this.breakingAllowed = (this.storage.getSessionStorageData(BREAKING_ALLLOWED_KEY, 'enabled') as any) !== 'disabled';

    this.sponsorship$ = this.config.sponsorship$;
    this.isMobile$ = this.config.isMobile$;
    (this.router.events.pipe(filter((event) => event instanceof NavigationStart)) as Observable<NavigationStart>).subscribe(() => {
      this.config.clearSponsorship();
      this.config.checkMobile();
    });

    // Reload Google Adsense script on every route change
    (this.router.events.pipe(filter((event) => event instanceof NavigationEnd)) as Observable<NavigationEnd>).subscribe((): void => {
      loadScript('https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8559195417632426');
      this.config.setHeader({ isDark: this.route.snapshot.data['isDark'] });

      // Reset back the initial scroll behavior from CSS.
      if (this.utils.isBrowser()) {
        document.querySelector('html')?.style.removeProperty('scroll-behavior');
      }
    });

    // any is necessary due to missing overlap of `NavigationEnd` and result of `router.events`
    combineLatest([
      this.adStoreAdo.isAdult,
      this.adStoreAdo.isArticleLoaded$,
      this.adStoreAdo.articleParentCategory$,
      this.router.events.pipe(
        filter((event) => event instanceof NavigationEnd),
        startWith(this.router)
      ),
    ])
      .pipe(
        mergeMap(() => {
          if (!this.utils.isBrowser() || !this.document?.location) {
            return of(null);
          }

          const [_, path1, path2] = this.document?.location?.pathname.split('/') ?? ['', ''];
          const parentCategory = this.adStoreAdo.articleParentCategory$.getValue();

          this.categorySlug = parentCategory || `column_${path2}`;

          this.isArticleUrl = !isNaN(parseInt(path2, 10));
          this.resetAds();

          if (this.isArticleUrl) {
            this.adStoreAdo.currentMasterIdSubject.next('');
          }

          return this.isArticleUrl && !this.adStoreAdo.isArticleLoaded$.getValue()
            ? of(null)
            : this.adStoreAdo.advertisemenets$.pipe(
                // We need delay to reinitialize the header adverts, otherwise this.resetAds function will not have any effect.
                delay(0),
                map((ads) => {
                  const advertPageType = this.baseElementPageTypeSwitch(path1).page;

                  return { ads, advertPageType };
                })
              );
        }),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((props) => {
        if (!this.utils.isBrowser() || !props || !props?.ads || !props?.advertPageType) {
          return;
        }

        this.adverts = this.adStoreAdo.separateAdsByMedium(props?.ads, props?.advertPageType, ALL_BANNER_LIST, SecondaryFilterAdvertType.REPLACEABLE);

        this.url = this.document?.location?.pathname;
        this.areAdsInitiated = true;

        this.changeRef.detectChanges();
      });

    this.apiService
      .getPortfolioFooter()
      .pipe(take(1))
      .subscribe((data: PortfolioResponse) => {
        this.mediaworksFooter = data.data;
      });

    const responseData: InitResolverData<{ tags: TrendingTag[] }> = this.route.snapshot.data?.['data'] ?? {};
    this.breakingNews = responseData?.init?.breakingNews;

    const {
      menu: { header, footer },
      tags,
    } = responseData || {};
    this.mainMenu = header ?? [];
    this.footer = footer ?? [];
    this.tags = tags ?? [];
  }

  public ngAfterViewInit(): void {
    this.adblockerActiveStatus();
    if (this.utils.isBrowser()) {
      this.initStickyObserver();

      const scroll$ = fromEvent(window, 'scroll');
      const resize$ = fromEvent(window, 'resize');

      merge(scroll$, resize$)
        .pipe(
          startWith(null),
          debounceTime(250),
          tap(() => {
            this.detectHeaderSizes();
          }),
          takeUntil(this.unsubscribe$)
        )
        .subscribe();
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
    if (this.stickyObserver) {
      this.stickyObserver.disconnect();
      this.stickyObserver = undefined;
    }
  }

  onCookieSettingsClick(): void {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }

  onSubscriptionClick(from: 'header' | 'footer'): void {
    const eventType = from === 'header' ? NEWSLETTER_COMPONENT_TYPE.HEADER : NEWSLETTER_COMPONENT_TYPE.FOOTER;
    this.analyticsService.newsLetterSubscriptionClicked(eventType);
  }

  public onBreakingCloseClick(): void {
    this.storage.setSessionStorageData(BREAKING_ALLLOWED_KEY, 'disabled');
    this.breakingAllowed = false;
    this.changeRef.detectChanges();
  }

  navigateToEbPage(): void {
    this.router.navigate(['/', 'labdarugo-eb-2024']);
  }

  navigateToOlimpiaPage(): void {
    this.router.navigate(['/', 'olimpia-2024']);
  }

  private initStickyObserver(): void {
    if (!this.utils.isBrowser()) {
      return;
    }
    this.stickyObserver = new IntersectionObserver(
      ([e]: IntersectionObserverEntry[]) => {
        if (e.boundingClientRect.top < 0) {
          if (!this.headerStuck() && e.intersectionRatio < 1) {
            window.scrollBy(0, 42);
            this.headerStuck.set(true);
          }
        } else {
          this.headerStuck.set(false);
        }
      },
      { threshold: [1] }
    );

    this.stickyObserver?.observe(this.headerElement.nativeElement);
  }

  private baseElementPageTypeSwitch(path: string): { page: string } {
    switch (path) {
      case '':
        this.isHomePage = true;
        return {
          page: PAGE_TYPES.main_page,
        };
      case 'rovat':
        this.isHomePage = false;
        return {
          page: this.categorySlug,
        };
      default:
        this.isHomePage = false;
        return {
          page: this.categorySlug,
        };
    }
  }

  private adblockerActiveStatus(): boolean | void {
    if (!this.utils.isBrowser()) {
      //Manually override to return false, because the ado does not exist on SSR.
      return;
    }
    this.isAdblockerActive = typeof window.ado !== 'object';
    return this.isAdblockerActive;
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.areAdsInitiated = false;
    this.changeRef.detectChanges();
  }

  private detectHeaderSizes(): void {
    if (this.utils.isBrowser()) {
      setTimeout(() => {
        const elementRect = this.headerElement?.nativeElement?.getBoundingClientRect();
        this.headerService.setHeaderTopOffset(elementRect?.top ?? 0);
        this.headerService.setHeaderHeight(elementRect?.height ?? 0);
      }, 0);
    }
  }
}
