import { Environment } from '@trendency/kesma-core';

// <PERSON><PERSON><PERSON> fejlesztői környezet
export const environment: Environment = {
  production: false,
  type: 'local',
  apiUrl: 'https://kozponti-api.dev.trendency.hu/publicapi/hu', // for proxy: '/publicapi/hu' then: npm run start-with-proxy
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  facebookAppId: '397495361000984',
  siteUrl: 'http://localhost:4200',
  googleClientId: 'AIzaSyDYqfTujUIi9kLZMhxt7y6Uz_h7DyRiWgY',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1',
  googleTagManager: 'GTM-WCLP7HF',
  gemiusId: 'nGGQy4hrd6I8io0RcV5gd8TYjwiNGKee.CV0Et2rztv.w7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: 'https://<EMAIL>/8',
    tracingOrigins: ['http://magyar-nemzet.dev.trendency.hu'],
    sampleRate: 1.0,
  },
};
