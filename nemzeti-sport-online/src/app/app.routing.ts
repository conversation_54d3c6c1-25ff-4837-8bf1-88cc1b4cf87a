import { Routes } from '@angular/router';
import { AuthGuard, BaseComponent, Error404Component, InitResolver, MobileAppGuard } from './shared';
import { CheckRedirectBefore404Guard, PageValidatorGuard } from '@trendency/kesma-ui';
import { NewsletterConfirmPageComponent } from './feature/newsletter/components/newsletter-confirm-page/newsletter-confirm-page.component';
import { NewsletterSuccessPageComponent } from './feature/newsletter/components/newsletter-success-page/newsletter-success-page.component';

export const appRoutes: Routes = [
  {
    path: 'layout-editor',
    resolve: { data: InitResolver },
    data: { skipSsrConditionalElements: true },
    loadChildren: () => import('./feature/layout-editor/layout-editor.routing').then((m) => m.layoutEditorRoutes),
  },
  {
    // Mobile App Article Body only endpoint
    path: 'mobile-article-body/:categorySlug/:year/:month/:articleSlug',
    loadChildren: () => import('./feature/article-page/article-page.routing').then((m) => m.articlePageRoutes),
    canActivate: [MobileAppGuard],
    data: {
      isMobileApp: true,
      omitGlobalPageView: true,
    },
  },
  {
    path: 'mobile-match/:slug',
    loadChildren: () => import('./feature/match/match.routing').then((m) => m.matchRouting),
    canActivate: [MobileAppGuard],
    data: {
      isMobileApp: true,
      omitGlobalPageView: true,
    },
  },
  {
    path: 'mobile-nso-news',
    loadChildren: () => import('./feature/news-page/news-page.routing').then((m) => m.NsoNewsPageRoutes),
    canActivate: [MobileAppGuard],
    data: {
      isMobileApp: true,
    },
  },
  {
    path: 'mobile-static-page/:slug',
    loadChildren: () => import('./feature/static-page/static-page.routing').then((m) => m.staticPageRoutes),
    canActivate: [MobileAppGuard],
    data: {
      isMobileApp: true,
    },
  },
  {
    path: 'mobile-gallery-page/galeriak',
    loadChildren: () => import('./feature/gallery-page/gallery-page.routing').then((m) => m.galleryPageRouting),
    canActivate: [MobileAppGuard],
    data: {
      isMobileApp: true,
    },
  },
  {
    path: 'mobile-cimke',
    loadChildren: () => import('./feature/tags-page/tags-page.routing').then((m) => m.tagsPageRoutes),
    canActivate: [MobileAppGuard],
    data: {
      isMobileApp: true,
    },
  },
  // régi nso-s cimke oldalak lekezelésére
  {
    path: 'mobile-nso_cimkeoldal',
    loadChildren: () => import('./feature/tags-page/tags-page.routing').then((m) => m.tagsPageRoutes),
    canActivate: [MobileAppGuard],
    data: {
      isMobileApp: true,
    },
  },
  {
    path: '',
    component: BaseComponent,
    resolve: { data: InitResolver },
    children: [
      {
        path: '',
        pathMatch: 'full',
        loadChildren: () => import('./feature/home/<USER>').then((m) => m.homeRoutes),
      },
      // "short-circuit" - ha a file nem létezik, az SSR lefut - ami file lehet, azt küldjük 404-re
      {
        path: 'assets/:file',
        redirectTo: '404',
      },
      {
        path: 'assets/:dir/:file',
        redirectTo: '404',
      },
      {
        path: 'script/:file',
        redirectTo: '404',
      },
      {
        path: 'mobil_nso_cikkhozzaszolas',
        loadChildren: () => import('./feature/article-page-nso-app/article-page-nso-app.routing').then((m) => m.articlePageNsoAppRoutes),
        data: {
          omitGlobalPageView: true,
        },
      },
      {
        path: 'nso-hirek',
        loadChildren: () => import('./feature/news-page/news-page.routing').then((m) => m.NsoNewsPageRoutes),
      },
      {
        path: 'galeria',
        loadChildren: () => import('./feature/gallery-layer/gallery-layer.routing').then((m) => m.galleryLayerRoutes),
      },
      {
        path: 'szerzo',
        loadChildren: () => import('./feature/author-page/author-page.routing').then((m) => m.authorRoutes),
      },
      {
        path: 'galeriak',
        loadChildren: () => import('./feature/gallery-page/gallery-page.routing').then((m) => m.galleryPageRouting),
      },
      {
        path: 'bajnoksag/:categorySlug',
        loadChildren: () => import('./feature/championship/championship.routing').then((c) => c.championshipRoutes),
      },
      {
        path: 'digitalis-napilap',
        loadChildren: () => import('./feature/digital-newspaper-list-page/digital-newspaper-list-page.routing').then((m) => m.digitalNewspapersListPageRoutes),
      },
      {
        path: 'napilap',
        loadChildren: () => import('./feature/newspaper-list-page/newspaper-list-page.routing').then((m) => m.newspapersListPageRoutes),
      },
      {
        path: 'rovat/:categorySlug',
        loadChildren: () => import('./feature/category-page/category.routing').then((m) => m.categoryRoutes),
        data: {
          omitGlobalPageView: true,
        },
      },
      {
        path: 'elrendezes-elonezet/:layoutHash',
        loadChildren: () => import('./feature/layout-preview/layout-preview.routing').then((m) => m.layoutPreviewRoutes),
      },
      {
        path: 'cimke',
        loadChildren: () => import('./feature/tags-page/tags-page.routing').then((m) => m.tagsPageRoutes),
      },
      {
        path: 'nso-tv',
        loadChildren: () => import('./feature/nso-tv-page/nso-tv-page.routing').then((m) => m.nsoTvPageRoutes),
      },
      {
        path: 'video/:slug',
        loadChildren: () => import('./feature/video/video.routing').then((m) => m.videoRoutes),
      },
      {
        path: 'kereses',
        loadChildren: () => import('./feature/search-page/search-page.routing').then((m) => m.searchPageRoutes),
      },
      {
        path: 'hirlevel/feliratkozas',
        loadChildren: () => import('./feature/newsletter/newsletter.routing').then((m) => m.newsletterRoutes),
        pathMatch: 'full',
        data: {
          isFullWidth: true,
        },
      },
      {
        path: 'hirlevel/feliratkozas-megerositese',
        component: NewsletterConfirmPageComponent,
        pathMatch: 'full',
      },
      {
        path: 'hirlevel/feliratkozas-megerosites-sikeres',
        component: NewsletterSuccessPageComponent,
        data: { pageTextSuffix: 'feliratkozás' },
        pathMatch: 'full',
      },
      {
        path: 'hirlevel/leiratkozas',
        component: NewsletterSuccessPageComponent,
        data: { pageTextSuffix: 'leiratkozás' },
        pathMatch: 'full',
      },
      {
        path: 'csapat/:teamSlug/:competitionSlug',
        loadChildren: () => import('./feature/team-page/team-page.routing').then((m) => m.teamPageRoutes),
      },
      {
        path: 'elofizetes',
        loadChildren: () => import('./feature/subscription/subscription.routing').then((m) => m.subscriptionRouting),
      },
      {
        path: 'regisztracio',
        data: {
          isFullWidth: true,
        },
        loadChildren: () => import('./feature/registration/registration.routing').then((m) => m.registrationRouting),
      },
      {
        path: 'bejelentkezes',
        data: {
          isFullWidth: true,
        },
        loadChildren: () => import('./feature/login/login.routing').then((m) => m.loginRouting),
      },
      {
        path: 'sport-radio',
        loadChildren: () => import('./feature/sport-radio/sport-radio.routing').then((m) => m.sportRadioRouting),
      },
      {
        path: 'merkozes/:slug',
        loadChildren: () => import('./feature/match/match.routing').then((m) => m.matchRouting),
      },
      {
        path: 'elfelejtett-jelszo',
        data: {
          isFullWidth: true,
        },
        loadChildren: () => import('./feature/forgot-password/forgot-password.routing').then((m) => m.forgotPasswordRouting),
      },
      {
        path: 'kijelentkezes',
        canActivate: [AuthGuard],
        loadChildren: () => import('./feature/logout/logout.routing').then((m) => m.logoutRouting),
      },
      {
        path: 'profil',
        data: {
          isFullWidth: true,
        },
        canActivate: [AuthGuard],
        loadChildren: () => import('./feature/profile/profile.routing').then((m) => m.profileRoutes),
      },
      {
        path: 'hirfolyam/:articleSlug',
        loadChildren: () => import('./feature/news-feed/news-feed.routing').then((m) => m.newsFeedRouting),
      },
      {
        path: 'my-nso',
        canActivate: [PageValidatorGuard, AuthGuard],
        loadChildren: () => import('./feature/my-nso/my-nso.routing').then((m) => m.myNsoRoutes),
      },
      {
        path: 'file/:fileId/:fileName',
        loadChildren: () => import('./feature/file/file.routing').then((m) => m.fileRoutes),
      },
      {
        path: 'cikk-elonezet/:previewHash',
        loadChildren: () => import('./feature/article-page/article-page.routing').then((m) => m.articlePageRoutes),
        data: {
          omitGlobalPageView: true,
        },
      },
      {
        path: 'cikk-elonezet/:previewHash/:previewType',
        loadChildren: () => import('./feature/article-page/article-page.routing').then((m) => m.articlePageRoutes),
        data: {
          omitGlobalPageView: true,
        },
      },
      {
        path: 'olimpia-2024/eremtablazat',
        loadChildren: () => import('./feature/olympics-medal-table/olympics-medal-table.routing').then((m) => m.olympicsMedalTableRoutes),
      },
      {
        path: '404',
        component: Error404Component,
        canActivate: [CheckRedirectBefore404Guard],
      },
      {
        // Hír
        path: ':categorySlug/:year/:month/:articleSlug',
        loadChildren: () => import('./feature/article-page/article-page.routing').then((m) => m.articlePageRoutes),
        data: {
          omitGlobalPageView: true,
        },
      },
      // Statikus oldalak
      {
        path: ':slug',
        loadChildren: () => import('./feature/static-page/static-page.routing').then((m) => m.staticPageRoutes),
      },
      {
        path: '**',
        redirectTo: '404',
      },
    ],
  },
];
