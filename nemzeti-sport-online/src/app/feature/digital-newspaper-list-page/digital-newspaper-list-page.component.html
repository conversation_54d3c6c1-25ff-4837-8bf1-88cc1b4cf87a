@if (articles(); as articles) {
  <section class="newspaper-page">
    <div class="wrapper newspaper-page-wrapper">
      <nso-breadcrumb [items]="[{ label: 'Digitális napilap' }]"></nso-breadcrumb>

      <h1 class="newspaper-page-title">Digit<PERSON><PERSON> napilap</h1>

      <div class="row newspaper-page-latest">
        <div class="col-12 col-md-5">
          <ng-container *ngTemplateOutlet="newspaper; context: { $implicit: articles?.[0] }"></ng-container>
        </div>
        <div class="col-12 col-md-7">
          <p>
            <strong>Kedves Olvasónk!</strong>
            <br /><br />
            Ezen a felületen digitális formában olvashatja nyomtatott lapszámainkat. Amennyiben van előfizetése, a gombra kattintva beléphet a digitális olvasó
            felületre, ahol végiglapozhatja a lapszámainkat. Amennyiben nem rendelkezik előfizetéssel, megrendelheti a szolgáltatást kiadónktól.
          </p>

          <div class="newspaper-page-buttons">
            <nso-simple-button [routerLink]="buildArticleUrl(articles?.[0])" round="round">BELÉPEK ÉS ELOLVASOM A LAPOT </nso-simple-button>
            <nso-simple-button [routerLink]="['/', 'elofizetes']" color="secondary" round="round">MEGRENDELEM A SZOLGÁLTATÁST </nso-simple-button>
          </div>

          <div class="newspaper-page-link">
            <a [routerLink]="['/', 'napilap']">Előfizetek a nyomtatott napilapra</a>
          </div>
        </div>
      </div>

      <div class="newspaper-page-list">
        <h2>Korábbi lapok</h2>
        <div class="row newspaper-page-list-wrapper">
          @for (article of articles | slice: 1; track article.id) {
            <div class="col-12 col-sm-6 col-md-3">
              <ng-container *ngTemplateOutlet="newspaper; context: { $implicit: article }"></ng-container>
            </div>
          }
        </div>
        <div class="newspaper-page-buttons">
          <nso-simple-button [routerLink]="['/', 'rovat', 'digitalis-napilap']" round="round">AZ ÖSSZES KORÁBBI SZÁM MEGTEKINTÉSE </nso-simple-button>
        </div>
      </div>

      <div class="newspaper-page-contact">
        <h3>Kérdése van?</h3>
        <p>Akár előfizetőnk, akár ajánlataink kapcsán kérdezne, kollégáink várják az Ön jelentkezését!</p>
        <div class="newspaper-page-contact-details">
          <p>Telefonszám: 06/1/999-6454</p>
          <p>
            E-mail:
            <a href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank">digitalislap&#64;mediaworks.hu</a>
          </p>
        </div>
      </div>
    </div>
  </section>
}

<ng-template #newspaper let-article>
  @if (article) {
    <a [routerLink]="buildArticleUrl(article)" class="newspaper-page-newspaper">
      <img
        withFocusPoint
        [data]="article?.thumbnailFocusedImages"
        [alt]="article.title"
        [displayedUrl]="article.thumbnail ?? '/assets/images/nemzetisport.png'"
        [displayedAspectRatio]="{ desktop: '16:9' }"
        loading="lazy"
      />
      <p>{{ article?.publishDate | dfnsFormat: 'yyyy. MM. dd.' }}</p>
    </a>
  }
</ng-template>
