import { ChangeDetectionStrategy, Component, Inject, OnInit, Signal } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { map } from 'rxjs/operators';
import { createNSOTitle, defaultMetaInfo, NsoBreadcrumbComponent, NsoSimpleButtonComponent } from '../../shared';
import { ArticleCard, buildArticleUrl, createCanonicalUrlForPageablePage, FocusPointDirective } from '@trendency/kesma-ui';
import { DOCUMENT, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { FormatPipeModule } from 'ngx-date-fns';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-digital-newspaper-list-page',
  templateUrl: './digital-newspaper-list-page.component.html',
  styleUrls: ['./digital-newspaper-list-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoBreadcrumbComponent, NgTemplateOutlet, NsoSimpleButtonComponent, RouterLink, SlicePipe, FocusPointDirective, FormatPipeModule],
})
export class DigitalNewspaperListPageComponent implements OnInit {
  articles: Signal<ArticleCard[]> = toSignal(this.route.data.pipe(map((res) => res?.['data'])));

  buildArticleUrl = buildArticleUrl;

  constructor(
    private readonly seo: SeoService,
    private readonly route: ActivatedRoute,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  ngOnInit(): void {
    this.setPageMeta();
  }

  setPageMeta(): void {
    const canonical = createCanonicalUrlForPageablePage('digitalis-napilap', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle(`Digitális napilap`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
