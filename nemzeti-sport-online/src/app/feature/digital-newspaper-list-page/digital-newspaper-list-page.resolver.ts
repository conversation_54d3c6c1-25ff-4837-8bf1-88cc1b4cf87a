import { ResolveFn, Router } from '@angular/router';
import { ArticleCard, BackendArticle, backendDateToDate } from '@trendency/kesma-ui';
import { inject } from '@angular/core';
import { ApiService } from '../../shared';
import { throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export const digitalNewspaperListPageResolver: ResolveFn<ArticleCard[]> = () => {
  const apiService = inject(ApiService);
  const router = inject(Router);

  return apiService.getCategoryArticles('digitalis-napilap', 0, 25).pipe(
    map((res) =>
      res?.data?.map(
        (article: BackendArticle): ArticleCard =>
          ({
            ...article,
            publishDate: backendDateToDate(article.publishDate as string) as Date,
          }) as ArticleCard
      )
    ),
    catchError((error) => {
      router.navigate(['/404'], {
        state: { errorResponse: JSON.stringify(error) },
        skipLocationChange: true,
      });
      return throwError(error);
    })
  );
};
