import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ArticleCard, backendDateToDate } from '@trendency/kesma-ui';
import { ApiService } from '../../shared';

@Injectable()
export class NewspaperListPageResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  resolve(): Observable<ArticleCard[]> {
    return this.apiService.getCategoryArticles('napilap', 0, 25).pipe(
      map((res) =>
        res?.data?.map(
          (article) =>
            ({
              ...article,
              publishDate: backendDateToDate(article.publishDate as string) as Date,
            }) as ArticleCard
        )
      ),
      catchError((err) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then(null);
        return throwError(err);
      })
    );
  }
}
