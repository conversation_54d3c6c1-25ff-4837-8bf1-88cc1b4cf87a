<ng-container *ngIf="data as article" [ngSwitch]="styleID">
  <ng-container *ngSwitchCase="ArticleCardType.FeaturedTitleArrowDate">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <h2 class="article-card-title">
          <span class="article-card-date">{{ publishDate | dfnsFormat: 'HH:mm' }}</span> —
          {{ article?.title }}
        </h2>
        <i class="icon icon-arrow-right-red"></i>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedTitleArrow">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <h2 class="article-card-title">{{ article?.title }}</h2>
        <i class="icon icon-arrow-right-red"></i>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedTitle">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <h2 class="article-card-title">{{ article?.title }}</h2>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedColumnTitleLead">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="categoryDateTemplate; context: { $implicit: DATE_FORMAT }"></ng-container>
        <h2 class="article-card-title">{{ article?.title }}</h2>
        <div class="article-card-lead">{{ article?.lead }}</div>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedColumnTitleLeadTags">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="categoryDateTemplate; context: { $implicit: DATE_FORMAT }"></ng-container>
        <h2 class="article-card-title">{{ article?.title }}</h2>
        <div class="article-card-lead">{{ article?.lead }}</div>
        <div class="article-card-tags">
          <ng-container *ngFor="let tag of article?.tags; last as last">
            <a class="article-card-column" clickStopPropagation [routerLink]="['/', 'cimke', tag?.slug]">{{ tag?.title }}</a>
            <div *ngIf="!last" class="article-card-divider"></div>
          </ng-container>
        </div>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.DataBankListItem">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '1:1' } }"></ng-container>
        <h2 class="article-card-title">{{ article?.title }}</h2>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedImgTitle">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
        <h2 class="article-card-title">{{ article?.title }}</h2>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedImgTitleDate">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
        <div class="article-card-date">{{ publishDate | publishDate: DATE_FORMAT }}</div>
        <h2 class="article-card-title">{{ article?.title }}</h2>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedImgColumnTitle">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
        <a clickStopPropagation [routerLink]="columnLink" class="article-card-column" [style.color]="data?.primaryColumnColorCombo?.color">
          {{ article?.category?.name }}
        </a>
        <h2 class="article-card-title">{{ article?.title }}</h2>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedImgTitleLead">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
        <h2 class="article-card-title">{{ article?.title }}</h2>
        <div class="article-card-lead">{{ article?.lead }}</div>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedImgColumnTitleDate">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
        <ng-container *ngTemplateOutlet="categoryDateTemplate; context: { $implicit: DATE_FORMAT }"></ng-container>
        <h2 class="article-card-title">{{ article?.title }}</h2>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedImgColumnTitleLeadDate">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
        <ng-container *ngTemplateOutlet="categoryDateTemplate; context: { $implicit: DATE_FORMAT }"></ng-container>
        <h2 class="article-card-title">{{ article?.title }}</h2>
        <div class="article-card-lead">{{ article?.lead }}</div>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedTitleInsideImg">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
        <h2 class="article-card-title">{{ article?.title }}</h2>
        <div class="article-card-shadow"></div>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.ExternalRecommendation">
    <a class="article-card-link" [href]="data?.url" target="_blank">
      <ng-container *ngTemplateOutlet="thumbnailTemplate"></ng-container>
      <div class="article-card-column">{{ article?.category?.name }}</div>
      <h2 class="article-card-title">{{ article?.title }}</h2>
    </a>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedSidedImgTitle">
    <div class="article-card-link">
      <ng-container
        *ngTemplateOutlet="
          thumbnailTemplate;
          context: {
            displayedAspectRatio: { desktop: desktopWidth <= 4 ? '16:9' : '4:3', mobile: '16:9' },
          }
        "
      >
      </ng-container>
      <nso-article-card-link-wrapper [data]="article">
        <h2 class="article-card-title">{{ article?.title }}</h2>
      </nso-article-card-link-wrapper>
    </div>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedSidedImgColumnTitleDate">
    <ng-container *ngIf="isMobile">
      <ng-container *ngTemplateOutlet="timeWithDateTemplate; context: { $implicit: DATE_FORMAT }"></ng-container>
    </ng-container>
    <div class="article-card-link">
      <ng-container
        *ngTemplateOutlet="
          thumbnailTemplate;
          context: {
            displayedAspectRatio: { desktop: '16:9', mobile: desktopWidth <= 4 ? '16:9' : '1:1' },
          }
        "
      >
      </ng-container>
      <div class="article-card-right">
        <ng-container *ngIf="!isMobile">
          <ng-container *ngTemplateOutlet="timeWithDateTemplate; context: { $implicit: DATE_FORMAT }"></ng-container>
        </ng-container>
        <a [routerLink]="columnLink" clickStopPropagation [style.color]="data?.primaryColumnColorCombo?.color" class="article-card-column">
          {{ article?.category?.name }}
        </a>
        <nso-article-card-link-wrapper [data]="article">
          <h2 class="article-card-title">{{ article?.title }}</h2>
        </nso-article-card-link-wrapper>
      </div>
    </div>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedSidedImgColumnTitleLead">
    <div class="article-card-link" [class.small]="desktopWidth <= 4">
      <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
      <div class="article-card-right">
        <ng-container *ngTemplateOutlet="categoryDateTemplate; context: { $implicit: DATE_FORMAT }"></ng-container>
        <nso-article-card-link-wrapper [data]="article">
          <h2 class="article-card-title">{{ article?.title }}</h2>
        </nso-article-card-link-wrapper>
        <div class="article-card-lead">{{ article?.lead }}</div>
      </div>
    </div>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedSidedImgBottomColumnTitleLead">
    <div class="article-card-link" [class.small]="desktopWidth <= 4">
      <ng-container
        *ngTemplateOutlet="
          thumbnailTemplate;
          context: {
            displayedAspectRatio: { desktop: desktopWidth <= 4 ? '16:9' : '4:3' },
          }
        "
      ></ng-container>
      <div class="article-card-right">
        <nso-article-card-link-wrapper [data]="article">
          <h2 class="article-card-title">{{ article?.title }}</h2>
        </nso-article-card-link-wrapper>
        <div class="article-card-lead">{{ article?.lead }}</div>
        <ng-container *ngTemplateOutlet="categoryDateTemplate; context: { $implicit: DATE_FORMAT }"></ng-container>
      </div>
    </div>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedSidedBottomColumnTitleDate">
    <div class="article-card-link" [class.small]="desktopWidth <= 4">
      <ng-container
        *ngTemplateOutlet="
          thumbnailTemplate;
          context: {
            displayedAspectRatio: { desktop: desktopWidth <= 4 ? '16:9' : '4:3', mobile: '16:9' },
          }
        "
      ></ng-container>
      <div class="article-card-right">
        <nso-article-card-link-wrapper [data]="article">
          <h2 class="article-card-title">{{ article?.title }}</h2>
        </nso-article-card-link-wrapper>
        <ng-container *ngTemplateOutlet="categoryDateTemplate; context: { $implicit: DATE_FORMAT }"></ng-container>
      </div>
    </div>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedSidedImgTitleLead">
    <nso-article-card-link-wrapper [data]="article">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
        <div class="article-card-right">
          <h2 class="article-card-title">{{ article?.title }}</h2>
          <div class="article-card-lead">{{ article?.lead }}</div>
        </div>
      </div>
    </nso-article-card-link-wrapper>
  </ng-container>

  <ng-container *ngSwitchCase="ArticleCardType.FeaturedBigSidedImgColumnTitleLead">
    <div class="article-card-link" [class.mobile]="desktopWidth <= 6">
      <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
      <div class="article-card-right">
        <ng-container *ngTemplateOutlet="categoryDateTemplate; context: { $implicit: DATE_FORMAT }"></ng-container>
        <nso-article-card-link-wrapper [data]="article">
          <h2 class="article-card-title">{{ article?.title }}</h2>
        </nso-article-card-link-wrapper>
        <div class="article-card-lead">{{ article?.lead }}</div>
      </div>
    </div>
  </ng-container>
</ng-container>

<ng-template #categoryDateTemplate>
  <div class="article-card-top">
    @if (columnLink && columnLink.length > 0) {
      <a
        clickStopPropagation
        [routerLink]="columnLink"
        [style.color]="data?.primaryColumnColorCombo?.color"
        class="article-card-column"
        [class.csupasport]="isCsupasport() && isColumn()"
        [class.hatsofuves]="isHatsofuves() && isColumn()"
        >{{ data?.category?.name }}</a
      >
    } @else {
      <nso-article-card-link-wrapper [data]="data" [style.color]="data?.primaryColumnColorCombo?.color" class="article-card-column">
        {{ data?.category?.name }}
      </nso-article-card-link-wrapper>
    }
    <div class="article-card-divider"></div>
    <div class="article-card-date">{{ publishDate | publishDate: 'yyyy.MM.dd. HH:mm' }}</div>
  </div>
</ng-template>

<ng-template #timeWithDateTemplate let-dateFormat>
  <div class="article-card-top">
    <div class="article-card-date" *ngIf="publishDate">{{ publishDate | formatDate: 'h-m' }}</div>
    <div class="article-card-divider"></div>
    <div class="article-card-date">{{ publishDate | date: dateFormat }}</div>
  </div>
</ng-template>

<ng-template #thumbnailTemplate let-displayedAspectRatio="displayedAspectRatio">
  <figure class="article-card-thumbnail-box">
    <ng-container *ngIf="styleID === ArticleCardType.ExternalRecommendation; else thumbnailWithRouterLink">
      <ng-container [ngTemplateOutlet]="thumbnailWithoutRouterLink"></ng-container>
    </ng-container>

    <ng-template #thumbnailWithoutRouterLink>
      <img
        class="article-card-thumbnail aspect-ratio-16:9"
        [class.small]="desktopWidth <= 4"
        [src]="displayedThumbnailUrl"
        [alt]="data?.thumbnail?.alt || ''"
        loading="lazy"
      />
      <ng-container [ngTemplateOutlet]="thumbnailIconsTemplate"></ng-container>
    </ng-template>
    <ng-template #thumbnailWithRouterLink>
      <nso-article-card-link-wrapper [data]="data">
        <img
          withFocusPoint
          [data]="data?.thumbnailFocusedImages"
          class="article-card-thumbnail"
          [class.small]="desktopWidth <= 4"
          [displayedUrl]="displayedThumbnailUrl"
          [displayedAspectRatio]="displayedAspectRatio"
          [alt]="data?.thumbnail?.alt || ''"
          loading="lazy"
        />
        <ng-container [ngTemplateOutlet]="thumbnailIconsTemplate"></ng-container>
      </nso-article-card-link-wrapper>
    </ng-template>
  </figure>
</ng-template>

<ng-template #thumbnailIconsTemplate>
  <div class="icon live" *ngIf="data?.minuteToMinute === minuteToMinute.RUNNING; else videoTemplate">&#8226; <strong>Élő</strong></div>
  <div class="gallery-indicator" *ngIf="data?.hasGallery">
    <i class="icon icon-gallery"></i>
  </div>
  <ng-template #videoTemplate>
    <i *ngIf="data?.isVideoType; else podcastTemplate" class="icon icon-play icon-video-play"></i>
  </ng-template>
  <ng-template #podcastTemplate>
    <i *ngIf="data?.isPodcastType" class="icon icon-podcast"></i>
  </ng-template>
</ng-template>
