{"root": true, "ignorePatterns": ["commitlint.config.js", "src/app/utils/*", "src/assets/*", "dist/*"], "overrides": [{"files": ["*.ts"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": ["tsconfig.json"], "createDefaultProgram": true}, "extends": ["plugin:@angular-eslint/recommended", "eslint:recommended", "plugin:@angular-eslint/template/process-inline-templates", "plugin:@typescript-eslint/recommended", "plugin:rxjs/recommended"], "plugins": ["functional", "import"], "rules": {"import/no-cycle": ["error"], "linebreak-style": ["error", "unix"], "@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": ["app"], "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": ["element", "attribute"], "prefix": ["app"], "style": "kebab-case"}], "@typescript-eslint/ban-types": ["error", {"types": {"Object": false}}], "arrow-parens": ["error", "always"], "no-trailing-spaces": "error", "max-len": ["error", {"code": 160, "tabWidth": 2, "ignorePattern": "^import .*"}], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "quotes": ["error", "single", {"allowTemplateLiterals": true}], "@typescript-eslint/no-explicit-any": "off", "functional/prefer-readonly-type": "off", "@typescript-eslint/prefer-readonly": ["error"], "rxjs/no-implicit-any-catch": "off", "@typescript-eslint/explicit-function-return-type": "error", "rxjs/finnish": ["error", {"functions": false, "methods": false, "parameters": false, "properties": false}], "@angular-eslint/no-output-on-prefix": "off"}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended"], "rules": {}}], "parserOptions": {"ecmaVersion": 2015}}