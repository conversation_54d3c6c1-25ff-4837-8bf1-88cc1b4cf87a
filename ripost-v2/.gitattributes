## GITATTRIBUTES FOR WEB PROJECTS
# Source: https://github.com/gitattributes/gitattributes/blob/master/Web.gitattributes
#
# Details per file setting:
#   text    These files should be normalized (i.e. convert CRLF to LF).
#   binary  These files are binary and should be left untouched.
######################################################################

# Auto detect
##   Handle line endings automatically for files detected as
##   text and leave all files detected as binary untouched.
##   This will handle all files NOT defined below.
*                 text=auto eol=lf

# Source code
*.bash            text eol=lf
*.coffee          text eol=lf
*.css             text diff=css eol=lf
*.htm             text diff=html eol=lf
*.html            text diff=html eol=lf
*.inc             text eol=lf
*.ini             text eol=lf
*.js              text eol=lf
*.mjs             text eol=lf
*.cjs             text eol=lf
*.json            text eol=lf
*.jsx             text eol=lf
*.less            text eol=lf
*.ls              text eol=lf
*.map             text -diff eol=lf
*.od              text eol=lf
*.onlydata        text eol=lf
*.php             text diff=php eol=lf
*.pl              text eol=lf
*.py              text diff=python eol=lf
*.rb              text diff=ruby eol=lf
*.sass            text eol=lf
*.scm             text eol=lf
*.scss            text diff=css eol=lf
*.sh              text eol=lf
.husky/*          text eol=lf
*.sql             text eol=lf
*.styl            text eol=lf
*.tag             text eol=lf
*.ts              text eol=lf
*.tsx             text eol=lf
*.xml             text eol=lf
*.xhtml           text diff=html eol=lf

# Docker
Dockerfile        text eol=lf

# Documentation
*.ipynb           text eol=lf
*.markdown        text diff=markdown eol=lf
*.md              text diff=markdown eol=lf
*.mdwn            text diff=markdown eol=lf
*.mdown           text diff=markdown eol=lf
*.mkd             text diff=markdown eol=lf
*.mkdn            text diff=markdown eol=lf
*.mdtxt           text eol=lf
*.mdtext          text eol=lf
*.txt             text eol=lf
AUTHORS           text eol=lf
CHANGELOG         text eol=lf
CHANGES           text eol=lf
CONTRIBUTING      text eol=lf
COPYING           text eol=lf
copyright         text eol=lf
*COPYRIGHT*       text eol=lf
INSTALL           text eol=lf
license           text eol=lf
LICENSE           text eol=lf
NEWS              text eol=lf
readme            text eol=lf
*README*          text eol=lf
TODO              text eol=lf

# Templates
*.dot             text eol=lf
*.ejs             text eol=lf
*.erb             text eol=lf
*.haml            text eol=lf
*.handlebars      text eol=lf
*.hbs             text eol=lf
*.hbt             text eol=lf
*.jade            text eol=lf
*.latte           text eol=lf
*.mustache        text eol=lf
*.njk             text eol=lf
*.phtml           text eol=lf
*.svelte          text eol=lf
*.tmpl            text eol=lf
*.tpl             text eol=lf
*.twig            text eol=lf
*.vue             text eol=lf

# Configs
*.cnf             text eol=lf
*.conf            text eol=lf
*.config          text eol=lf
.editorconfig     text eol=lf
.env              text eol=lf
.gitattributes    text eol=lf
.gitconfig        text eol=lf
.htaccess         text eol=lf
*.lock            text -diff
package.json      text eol=lf
package-lock.json text eol=lf -diff
pnpm-lock.yaml    text eol=lf -diff
.prettierrc       text eol=lf
yarn.lock         text -diff eol=lf
*.toml            text eol=lf
*.yaml            text eol=lf
*.yml             text eol=lf
browserslist      text eol=lf
Makefile          text eol=lf
makefile          text eol=lf
# Fixes syntax highlighting on GitHub to allow comments
tsconfig.json     linguist-language=JSON-with-Comments eol=lf

# Heroku
Procfile          text eol=lf

# Graphics
*.ai              binary
*.bmp             binary
*.eps             binary
*.gif             binary
*.gifv            binary
*.ico             binary
*.jng             binary
*.jp2             binary
*.jpg             binary
*.jpeg            binary
*.jpx             binary
*.jxr             binary
*.pdf             binary
*.png             binary
*.psb             binary
*.psd             binary
# SVG treated as an asset (binary) by default.
*.svg             text eol=lf
# If you want to treat it as binary,
# use the following line instead.
# *.svg           binary
*.svgz            binary
*.tif             binary
*.tiff            binary
*.wbmp            binary
*.webp            binary

# Audio
*.kar             binary
*.m4a             binary
*.mid             binary
*.midi            binary
*.mp3             binary
*.ogg             binary
*.ra              binary

# Video
*.3gpp            binary
*.3gp             binary
*.as              binary
*.asf             binary
*.asx             binary
*.avi             binary
*.fla             binary
*.flv             binary
*.m4v             binary
*.mng             binary
*.mov             binary
*.mp4             binary
*.mpeg            binary
*.mpg             binary
*.ogv             binary
*.swc             binary
*.swf             binary
*.webm            binary

# Archives
*.7z              binary
*.gz              binary
*.jar             binary
*.rar             binary
*.tar             binary
*.zip             binary

# Fonts
*.ttf             binary
*.eot             binary
*.otf             binary
*.woff            binary
*.woff2           binary

# Executables
*.exe             binary
*.pyc             binary
# Prevents massive diffs caused by vendored, minified files
**/.yarn/releases/**   binary
**/.yarn/plugins/**    binary

# RC files (like .babelrc or .eslintrc)
*.*rc             text

# Ignore files (like .npmignore or .gitignore)
*.*ignore         text

# Prevents massive diffs from built files
dist/*            binary
