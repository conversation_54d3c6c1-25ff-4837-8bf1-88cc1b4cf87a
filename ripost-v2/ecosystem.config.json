{"apps": [{"name": "ripost-dev", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4301}}, {"name": "ripost-test", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "exec_mode": "cluster", "instances": "2", "cwd": "/content/apps/ripostfe/app", "max_restarts": 10, "env": {"PORT": 30055, "HTTPS_PROXY": "http://pkg-trendency:<EMAIL>:3128"}, "out_file": "/content/logs/ripostfe/out.log", "err_file": "/content/logs/ripostfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "ripost-prod", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "exec_mode": "cluster", "instances": "4", "cwd": "/content/apps/ripostfe/app", "max_restarts": 10, "env": {"PORT": 30055, "HTTPS_PROXY": "http://trendency-prd:<EMAIL>:3128"}, "out_file": "/content/logs/ripostfe/out.log", "err_file": "/content/logs/ripostfe/err.log", "log_type": "json", "time": true, "merge_logs": true}]}