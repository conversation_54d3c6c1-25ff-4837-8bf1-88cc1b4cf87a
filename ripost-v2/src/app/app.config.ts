import { ApplicationConfig, importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling } from '@angular/router';
import { routes } from './app.routes';
import { provideClientHydration } from '@angular/platform-browser';
import { AppEnvironment, provideEncodedTransferState } from '@trendency/kesma-core';
import { provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import { GoogleTagManagerModule } from 'angular-google-tag-manager';
import { environment } from '../environments/environment';
import { provideAnimations } from '@angular/platform-browser/animations';
import { portalHeaderHttpInterceptor } from './shared';

const GTAG_PROVIDER = [{ provide: 'googleTagManagerId', useValue: environment.googleTagManager }];

export const appConfig: ApplicationConfig = {
  providers: [
    provideEncodedTransferState(),
    provideAnimations(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withEnabledBlockingInitialNavigation(), withInMemoryScrolling({ scrollPositionRestoration: 'enabled', anchorScrolling: 'enabled' })),
    provideClientHydration(),
    provideHttpClient(withInterceptorsFromDi(), withInterceptors([portalHeaderHttpInterceptor])),
    importProvidersFrom(GoogleTagManagerModule),
    {
      provide: AppEnvironment,
      useValue: environment,
    },
    ...GTAG_PROVIDER,
  ],
};
