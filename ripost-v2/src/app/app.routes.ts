import { Routes } from '@angular/router';
import { BaseComponent, initResolver } from './shared';
import { CheckRedirectBefore404Guard } from '@trendency/kesma-ui';

export const routes: Routes = [
  {
    path: 'layout-editor',
    loadChildren: () => import('./feature/layout-editor/layout-editor.routing').then((m) => m.layoutEditorRoutes),
  },
  {
    path: '',
    component: BaseComponent,
    resolve: { data: initResolver },
    children: [
      {
        path: '',
        pathMatch: 'full',
        loadChildren: () => import('./feature/home/<USER>').then((m) => m.HOME_ROUTES),
      },
      // "short-circuit" - ha a file nem létezik, az SSR lefut - ami file lehet, azt küldjük 404-re
      {
        path: 'assets/:file',
        redirectTo: '404',
      },
      {
        path: 'assets/:dir/:file',
        redirectTo: '404',
      },
      {
        path: 'script/:file',
        redirectTo: '404',
      },
      {
        path: 'rovat',
        loadChildren: () => import('./feature/category/category.routing').then((m) => m.CATEGORY_ROUTES),
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
      },
      {
        path: 'cimke',
        loadChildren: () => import('./feature/tags-page/tags-page.routing').then((m) => m.TAGS_PAGE_ROUTES),
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
      },
      {
        path: 'galeriak',
        pathMatch: 'full',
        loadChildren: () => import('./feature/galleries/galleries.routing').then((m) => m.GALLERIES_ROUTES),
      },
      {
        path: 'szerzo',
        loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHORS_PAGE_ROUTES),
      },
      {
        path: 'szerzok',
        loadChildren: () => import('./feature/authors/authors.routing').then((m) => m.AUTHORS_PAGE_ROUTES),
      },
      {
        path: 'cikk-elonezet/:previewHash',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: 'cikk-elonezet/:previewHash/:previewType',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: 'cikk-elonezet/:previewHash/:previewType',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          omitGlobalPageView: true,
        },
      },
      {
        path: 'elrendezes-elonezet/:layoutHash',
        loadChildren: () => import('./feature/layout-preview/layout-preview.routing').then((m) => m.LAYOUT_PREVIEW_ROUTES),
      },
      {
        path: '404',
        loadComponent: () => import('./shared/components/404/404.component').then((m) => m.Error404Component),
        canActivate: [CheckRedirectBefore404Guard],
      },
      {
        path: ':categorySlug/:year/:month/:articleSlug',
        loadChildren: () => import('./feature/article/article.routes').then((m) => m.ARTICLE_ROUTES),
        data: {
          skipSeoMetaCheck: true,
          showRecommendations: true,
        },
      },
      {
        path: ':slug',
        loadChildren: () => import('./feature/static-page/static-page.routing').then((m) => m.STATIC_PAGE_ROUTES),
        data: {
          omitGlobalPageView: true,
        },
      },
      {
        path: '**',
        redirectTo: '404',
      },
    ],
  },
];
