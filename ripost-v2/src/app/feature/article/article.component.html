@if (!isUserAdultChoice() && article()?.isAdultsOnly && false) {
  <!-- FIXME: && false -->
  <!--<app-adult (isUserAdult)="onIsUserAdultChoose($event)"></app-adult>-->
} @else {
  <section class="article">
    <div class="wrapper with-aside">
      <div class="left-column">
        @if (adverts()?.desktop?.leaderboard_1; as ad) {
          <div class="leaderboard-1-ad">
            <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
          </div>
        }

        @if (article()?.lead || article()?.excerpt) {
          <p class="article-lead">{{ article()?.lead || article()?.excerpt }}</p>
        }
        @if (adverts()?.desktop?.roadblock_1; as ad) {
          <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
        }
        @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
          <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
        }

        @for (element of article()?.body; track element.id) {
          @switch (element.type) {
            @case (ArticleBodyType.Wysywyg) {
              @for (wysiwygDetail of element?.details; track wysiwygDetail) {
                <app-wysiwyg-box [html]="wysiwygDetail?.value"></app-wysiwyg-box>
              }
            }
            @case (ArticleBodyType.Advert) {
              @if (interrupter()?.mobile?.[$any(element).adverts.mobile]; as ad) {
                <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
              }
              @if (interrupter()?.desktop?.[$any(element).adverts.desktop]; as ad) {
                <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
              }
            }
            @case (ArticleBodyType.Voting) {
              <app-voting [data]="element?.details[0]?.value"></app-voting>
            }
            @case (ArticleBodyType.SubsequentDossier) {
              FIXME: ArticleBodyType.SubsequentDossier
            }
            @case (ArticleBodyType.Quiz) {
              <app-quiz [data]="element?.details[0]?.value"></app-quiz>
            }
            @case (ArticleBodyType.MediaVideo) {
              FIXME: ArticleBodyType.MediaVideo
            }
            @case (ArticleBodyType.Gallery) {
              FIXME: ArticleBodyType.Gallery
            }
            @case (ArticleBodyType.Article) {
              FIXME: ArticleBodyType.Article
            }
          }
        }

        @if (adverts()?.desktop?.roadblock_2; as ad) {
          <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
        }
        @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
          <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
        }
      </div>
      <aside>
        <app-sidebar
          [articleId]="article()?.id"
          [articleSlug]="article()?.slug"
          [categorySlug]="article()?.primaryColumn?.slug"
          [adPageType]="adPageType()"
        ></app-sidebar>
      </aside>
    </div>
  </section>
}
