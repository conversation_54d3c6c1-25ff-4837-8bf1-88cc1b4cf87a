import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnDestroy, OnInit, signal } from '@angular/core';
import {
  Advertisement,
  Article,
  RecommendationsData,
  ApiResponseMetaList,
  MinuteToMinuteBlock,
  PAGE_TYPES,
  ArticleBodyType,
  AdvertisementAdoceanComponent,
  AdvertisementsByMedium,
  AutoArticleBodyAdService,
  AnalyticsService,
  SearchBotService,
  AdvertisementAdoceanStoreService,
} from '@trendency/kesma-ui';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { combineLatest, Observable } from 'rxjs';
import { map, switchMap, tap } from 'rxjs/operators';
import { FormatDatePipe, SeoService, StorageService } from '@trendency/kesma-core';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { WysiwygBoxComponent, defaultMetaInfo, mapAdvertsToBody, VotingComponent } from '../../shared';
import { QuizComponent } from '../../shared/components/quiz/quiz.component';

const ADULT_CHOICE_STORAGE_KEY = 'isAdultChoice';

@Component({
  selector: 'app-article',
  templateUrl: './article.component.html',
  styleUrl: './article.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SidebarComponent, WysiwygBoxComponent, AdvertisementAdoceanComponent, VotingComponent, QuizComponent],
  providers: [AutoArticleBodyAdService, FormatDatePipe],
})
export class ArticleComponent implements OnInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly storage = inject(StorageService);
  private readonly articleBodyAdService = inject(AutoArticleBodyAdService);
  private readonly seoService = inject(SeoService);
  private readonly analyticsService = inject(AnalyticsService);
  private readonly searchBotService = inject(SearchBotService);
  private readonly formatDate = inject(FormatDatePipe);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);

  readonly article = signal<Article>({} as Article);
  readonly articleMeta = signal<ApiResponseMetaList | null>(null);
  readonly recommendations = signal<RecommendationsData | null>(null);
  readonly isUserAdultChoice = signal<boolean>(false);
  readonly minuteToMinuteBlocks = signal<MinuteToMinuteBlock[]>([]);
  readonly adPageType = signal<string>(PAGE_TYPES.all_articles_and_sub_pages);
  readonly adverts = signal<AdvertisementsByMedium | null>(null);
  readonly interrupter = signal<AdvertisementsByMedium | null>(null); // FIXME: Not implemented yet!
  readonly canonicalUrl = signal<string>('');
  readonly isSearchBot = signal<boolean>(false);

  isExceptionAdvertEnabled = false;

  readonly ArticleBodyType = ArticleBodyType;

  ngOnInit(): void {
    this.route.data
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        map(({ data }) => data)
      )
      .subscribe(({ article: articleResponse, recommendations }) => {
        this.articleBodyAdService.init(articleResponse.data.body);
        const articleBody = this.articleBodyAdService.autoAd();
        const article = articleResponse.data;

        this.article.set({
          ...articleResponse.data,
          body: mapAdvertsToBody(articleBody),
        });

        this.recommendations.set(recommendations.data);
        this.isUserAdultChoice.set((this.storage.getSessionStorageData(ADULT_CHOICE_STORAGE_KEY, false) ?? false) && article?.isAdultsOnly);
        this.articleMeta.set(articleResponse.meta);
        this.minuteToMinuteBlocks.set(article?.minuteToMinuteBlocks ?? []);
        this.setAdMetaAndPageType(article);
        this.canonicalUrl.set(article?.seo?.seoCanonicalUrl || article?.canonicalUrl || '');

        this.setMetaData();
        this.sendPageView();
      });

    this.initAds();
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice.set(isUserAdult);
    this.adStoreAdo.setIsAdultPage(isUserAdult);
  }

  setAdMetaAndPageType(article: Article): void {
    this.adPageType.set(`column_${article?.primaryColumn?.slug}`);
    this.adStoreAdo.setArticleParentCategory(this.adPageType());
    this.adStoreAdo.getAdvertisementMeta(article?.tags, article?.isAdultsOnly);
  }

  private initAds(): void {
    (
      combineLatest([
        this.route.data as Observable<{
          data: { article: { data: Article; meta: ApiResponseMetaList } };
        }>,
        this.adStoreAdo.isAdult.asObservable(),
      ]) as Observable<any>
    )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .pipe(
        map<[{ data: { article: { data: Article; meta: ApiResponseMetaList } } }, boolean], boolean | undefined>(
          ([
            {
              data: { article },
            },
            _isAdult,
          ]) => {
            // RESET ADOCEAN META TAGS
            const advertMeta = this.adStoreAdo.advertMeta$.getValue();
            this.adStoreAdo.advertMeta$.next({
              vars: advertMeta.vars,
              keys: '',
            });
            this.isExceptionAdvertEnabled = article?.data?.isExceptionAdvertEnabled;
            return article?.data?.withoutAds;
          }
        ),
        switchMap((withoutAds) => {
          this.resetAds();
          withoutAds ? this.adStoreAdo.disableAds() : this.adStoreAdo.enableAds();
          return this.adStoreAdo.advertisemenets$;
        })
      )
      .subscribe((adsCollection): void => {
        this.adverts.set(this.adStoreAdo.separateAdsByMedium(adsCollection, this.adPageType()));
        this.adStoreAdo.onArticleLoaded();
      });
  }

  private resetAds(): void {
    this.adverts.set(null);
  }

  ngOnDestroy(): void {
    this.adStoreAdo.onArticleDestroy();
  }

  private sendPageView(): void {
    const lastUpdatedAt = (this.article()?.lastUpdated || this.article().publishDate) as Date;
    setTimeout(() => {
      this.analyticsService.sendPageView({
        pageCategory: this.article()?.primaryColumn?.parent?.slug || this.article()?.primaryColumn?.slug || '',
        customDim2: this.article()?.topicLevel1,
        customDim1: this.article()?.aniCode,
        title: this.article().title,
        articleSource: this.article()?.articleSource || 'no source',
        publishDate: this.formatDate.transform(this.article()?.publishDate as Date, 'dateTime'),
        lastUpdatedDate: this.formatDate.transform(lastUpdatedAt, 'dateTime'),
      });
    });
    this.isSearchBot.set(this.searchBotService.isSearchBot());
    this.article()?.isAdultsOnly ? this.searchBotService.insertAdultMetaTag() : this.searchBotService.removeAdultMetaTag();
  }

  private setMetaData(): void {
    const { lead, thumbnail, publicAuthor, publishDate, alternativeTitle } = this.article() || {};
    if (!this.article()) {
      return;
    }

    const finalTitle = alternativeTitle || this.article()?.seo?.seoTitle || this.article()?.title;
    const finalOgTitle = alternativeTitle || this.article()?.title;

    this.seoService.setMetaData({
      title: finalTitle,
      description: this.article()?.seo?.seoDescription || lead || defaultMetaInfo.description,
      ogTitle: finalOgTitle,
      ogImage: thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
      robots: this.article()?.seo?.seoRobotsMeta,
    });

    //By default, the updateCanonicalUrl prefixes the url with the host, so we pass a second parameter to force absolute url.
    if (this.article()?.seo?.seoCanonicalUrl) {
      this.seoService.updateCanonicalUrl(this.canonicalUrl());
    }
  }
}
