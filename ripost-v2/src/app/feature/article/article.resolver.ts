import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { ApiResult, Article, ArticleRouteParams, RecommendationsData } from '@trendency/kesma-ui';
import { forkJoin, Observable, of, switchMap, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { ArticleService } from '../../shared';

type ArticleResolverResponse = Readonly<{
  article: ApiResult<Article>;
  recommendations: ApiResult<RecommendationsData>;
}>;

export const articleResolver: ResolveFn<ArticleResolverResponse> = (route: ActivatedRouteSnapshot) => {
  const router = inject(Router);
  const articleService = inject(ArticleService);

  const params = route.params as ArticleRouteParams;
  const previewType = params?.previewType || 'accepted';
  const { previewHash, categorySlug, articleSlug } = params;
  const isYear = !isNaN(parseInt(params.year as string));
  const year = isYear && params.year ? params.year : undefined;
  const month = isYear && params.month ? params.month : undefined;

  if ((!year || !month) && !previewHash) {
    router
      .navigate(['/', '404'], {
        skipLocationChange: true,
      })
      .then();
  }

  const request$: Observable<ArticleResolverResponse> = of({}).pipe(
    switchMap(() => {
      if (previewHash) {
        return forkJoin({
          article: articleService.getArticlePreview('cikk-elonezet', previewHash, previewType),
          recommendations: of({} as ApiResult<RecommendationsData>),
        });
      }
      return forkJoin({
        article: articleService.getArticle(categorySlug, String(year), String(month), articleSlug),
        recommendations: articleService.getArticleRecommendations(articleSlug),
      });
    })
  );

  return request$.pipe(
    catchError((error: HttpErrorResponse | Error) => {
      console.error('Article page resolver error', error);
      router
        .navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        })
        .then();
      return throwError(() => error);
    })
  );
};
