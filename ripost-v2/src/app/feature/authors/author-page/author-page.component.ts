import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { map, switchMap } from 'rxjs/operators';
import { AuthorData, AuthorPageData } from '../author.definitions';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, ArticleCard } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../../../shared';

@Component({
  selector: 'app-author-page',
  imports: [ArticleCardComponent, AdvertisementAdoceanComponent],
  templateUrl: './author-page.component.html',
  styleUrl: './author-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuthorPageComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as AuthorPageData)));
  readonly author: Signal<AuthorData | undefined> = computed(() => this.resolverData()?.author);
  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.articles);

  readonly adverts = toSignal(
    this.route.data.pipe(
      switchMap(() => this.adStoreAdo.advertisemenets$),
      map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads)),
      takeUntilDestroyed(this.destroyRef)
    )
  );
}
