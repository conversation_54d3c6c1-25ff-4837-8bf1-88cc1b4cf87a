import { ChangeDetectionStrategy, Component, computed, inject, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { AuthorData, AuthorPageData } from '../author.definitions';
import { ArticleCard } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../../../shared';

@Component({
  selector: 'app-author-page',
  imports: [ArticleCardComponent],
  templateUrl: './author-page.component.html',
  styleUrl: './author-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuthorPageComponent {
  private readonly route = inject(ActivatedRoute);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as AuthorPageData)));
  readonly author: Signal<AuthorData | undefined> = computed(() => this.resolverData()?.author);
  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.articles);
}
