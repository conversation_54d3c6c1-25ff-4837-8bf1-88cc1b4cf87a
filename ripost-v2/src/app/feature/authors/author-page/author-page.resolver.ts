import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { catchError, forkJoin, map, share, switchMap, take, throwError } from 'rxjs';
import { ArticleSearchResult, PortalConfigSetting, RedirectService } from '@trendency/kesma-ui';
import { tap } from 'rxjs/operators';
import { inject } from '@angular/core';
import { ApiService, PortalConfigService, searchResultToArticleCard } from '../../../shared';
import { AuthorsService } from '../auhtors.service';
import { AuthorPageData } from '../author.definitions';
import { mapSocialAuthorToAuthor } from './author-page.utils';

export const authorPageResolver: ResolveFn<AuthorPageData> = (route: ActivatedRouteSnapshot) => {
  const authorsService = inject(AuthorsService);
  const apiService = inject(ApiService);
  const portalConfigService = inject(PortalConfigService);
  const redirectService = inject(RedirectService);
  const router = inject(Router);

  const authorSlug = route.paramMap.get('authorSlug');
  const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;

  const publicAuthor$ = authorsService.getPublicAuthorSocial(authorSlug as string).pipe(
    share(),
    take(1),
    map(({ data }) => mapSocialAuthorToAuthor(data as any))
  );

  const articlesObservable$ = publicAuthor$.pipe(
    take(1),
    switchMap((author) => {
      if (!author?.id) {
        router.navigate(['/', '404'], { skipLocationChange: true }).then();
        throwError(() => 'Nincs ilyen szerző');
      }
      const authorFilter = portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_EXTERNAL_PUBLIC_AUTHOR_M2M)
        ? { 'author[]': author.id }
        : { author: author.publicAuthorName };
      return apiService
        .searchArticles(currentPage, 10, {
          global_filter: route.queryParams['global_filter'] || '',
          'publishDate_order[]': route.queryParams['publishDate_order[]'] === 'asc' ? 'asc' : 'desc',
          ...authorFilter,
        })
        .pipe(
          tap(({ data }) => {
            if (redirectService.shouldBeRedirect(currentPage, data)) {
              redirectService.redirectOldUrl(`szerzo/${authorSlug}`, false, 302);
            }
          })
        );
    })
  );

  return forkJoin({
    articles: articlesObservable$,
    author: publicAuthor$,
  }).pipe(
    map(({ articles, author }) => ({
      articles: articles?.data?.map((sr: ArticleSearchResult) => searchResultToArticleCard(sr)),
      limitable: articles?.meta?.limitable,
      author,
    })),
    tap(({ articles }) => {
      if (redirectService.shouldBeRedirect(currentPage, articles)) {
        redirectService.redirectOldUrl(`szerzo/${authorSlug}`, false, 302);
      }
    }),
    catchError((err) => {
      router.navigate(['/404'], { skipLocationChange: true }).then();
      return throwError(() => err);
    })
  );
};
