import { ChangeDetectionStrategy, Component, computed, inject, Signal } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { ApiResponseMetaList, ApiResult, BackendAuthorData } from '@trendency/kesma-ui';

@Component({
  selector: 'app-authors-list',
  imports: [RouterLink],
  templateUrl: './authors-list.component.html',
  styleUrl: './authors-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuthorsListComponent {
  private readonly route = inject(ActivatedRoute);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as ApiResult<BackendAuthorData[], ApiResponseMetaList>)));
  readonly authors: Signal<BackendAuthorData[] | undefined> = computed(() => this.resolverData()?.data);
}
