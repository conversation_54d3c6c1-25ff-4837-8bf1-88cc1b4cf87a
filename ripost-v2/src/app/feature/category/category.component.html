<div class="wrapper with-aside">
  <h1 class="page-title">{{ columnTitle() }}</h1>

  @if (adverts()?.desktop?.leaderboard_1; as ad) {
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  }

  @if (adverts()?.desktop?.roadblock_1; as ad) {
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  }
  @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  }

  <div class="articles">
    @for (article of articles(); track article.id; let i = $index) {
      <app-article-card [data]="article"></app-article-card>

      @if (i === 4) {
        @if (adverts()?.desktop?.roadblock_2; as ad) {
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
        }
        @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
        }
      }
    }
  </div>
</div>
