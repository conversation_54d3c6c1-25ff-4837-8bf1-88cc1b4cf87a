import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnDestroy, signal, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, AnalyticsService, ArticleCard } from '@trendency/kesma-ui';
import { ArticleCardComponent, CategoryResolverResponse } from '../../shared';
import { map, switchMap, tap } from 'rxjs/operators';

@Component({
  selector: 'app-category',
  imports: [ArticleCardComponent, AdvertisementAdoceanComponent],
  templateUrl: './category.component.html',
  styleUrl: './category.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CategoryComponent implements OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly analyticsService = inject(AnalyticsService);

  readonly adPageType = signal<string>('');

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as CategoryResolverResponse)));
  readonly columnTitle: Signal<string | undefined> = computed(() => this.resolverData()?.columnTitle);
  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.category?.data);

  readonly adverts = toSignal(
    this.route.data.pipe(
      tap(({ data }) => {
        this.adPageType.set(`column_${data.slug}`);
        this.adStoreAdo.setArticleParentCategory(this.adPageType());
      }),
      switchMap(() => this.adStoreAdo.advertisemenets$),
      map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads, this.adPageType())),
      takeUntilDestroyed(this.destroyRef)
    )
  );

  ngOnDestroy(): void {
    this.adStoreAdo.setArticleParentCategory('');
  }
}
