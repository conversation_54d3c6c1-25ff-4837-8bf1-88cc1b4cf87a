import { ChangeDetectionStrategy, Component, computed, inject, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { ArticleCard } from '@trendency/kesma-ui';
import { ArticleCardComponent, CategoryResolverResponse } from '../../shared';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-category',
  imports: [ArticleCardComponent],
  templateUrl: './category.component.html',
  styleUrl: './category.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CategoryComponent {
  private readonly route = inject(ActivatedRoute);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as CategoryResolverResponse)));
  readonly columnTitle: Signal<string | undefined> = computed(() => this.resolverData()?.columnTitle);
  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.category?.data);
}
