import { CategoryResolverResponse } from '../../shared';
import { ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { CategoryService } from './category.service';
import { catchError, throwError } from 'rxjs';

export const categoryPageResolver: ResolveFn<CategoryResolverResponse> = ({ params, queryParams }) => {
  const categoryService = inject(CategoryService);
  const router = inject(Router);

  return categoryService.getRequestForCategoryLayout(params, queryParams).pipe(
    catchError((error) => {
      router
        .navigate(['/404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        })
        .then();
      return throwError(error);
    })
  );
};
