import { inject, Injectable } from '@angular/core';
import { ApiService, CategoryResolverResponse, mapCategoryResponse } from '../../shared';
import { ApiResponseMetaList, ApiResult, ArticleCard, LayoutService, LayoutWithExcludeIds, RedirectService } from '@trendency/kesma-ui';
import { Params, Router } from '@angular/router';
import { Observable, switchMap, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';

export const MAX_RESULTS_PER_PAGE = 20;

@Injectable({
  providedIn: 'root',
})
export class CategoryService {
  private readonly apiService = inject(ApiService);
  private readonly layoutService = inject(LayoutService);
  private readonly router = inject(Router);
  private readonly redirectService = inject(RedirectService);

  getRequestForCategoryLayout(params: Params, queryParams: Params): Observable<CategoryResolverResponse> {
    const { categorySlug } = params;
    const { page } = queryParams;
    const pageIndex = page ? page - 1 : 0;

    return this.layoutService.getLayoutWithExcludeIds(categorySlug).pipe(
      tap((data: LayoutWithExcludeIds) => {
        if (!data.columnTitle) {
          this.router
            .navigate(['/', '404'], {
              state: {
                errorResponse: JSON.stringify(data),
              },
              skipLocationChange: true,
            })
            .then();
        }
      }),
      switchMap((layoutResponse: LayoutWithExcludeIds) => {
        return this.apiService.getCategoryArticles(categorySlug, pageIndex, MAX_RESULTS_PER_PAGE, undefined, undefined, layoutResponse.excludedIds).pipe(
          catchError((error) => {
            this.router
              .navigate(['/', '404'], {
                state: {
                  errorResponse: JSON.stringify(error),
                },
                skipLocationChange: true,
              })
              .then();
            return throwError(error);
          }),
          map((res) => {
            if (this.redirectService.shouldBeRedirect(pageIndex, res?.data)) {
              this.redirectService.redirectOldUrl(`rovat/${categorySlug}`, false, 302);
            }
            return res as ApiResult<ArticleCard[], ApiResponseMetaList>;
          }),
          map((categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>) =>
            mapCategoryResponse(categoryResponse, categorySlug, undefined, undefined, layoutResponse)
          )
        );
      })
    );
  }
}
