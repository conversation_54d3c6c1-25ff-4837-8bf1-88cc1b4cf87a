import { ChangeDetectionStrategy, Component, inject, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { ApiResponseMetaList, ApiResult, createCanonicalUrlForPageablePage, GalleryData } from '@trendency/kesma-ui';
import { map, tap } from 'rxjs/operators';
import { SeoService } from '@trendency/kesma-core';
import { createRipostTitle, defaultMetaInfo } from '../../shared';

@Component({
  selector: 'app-galleries',
  templateUrl: './galleries.component.html',
  styleUrl: './galleries.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GalleriesComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);

  readonly galleries: Signal<ApiResult<GalleryData[], ApiResponseMetaList>> = toSignal(
    this.route.data.pipe(
      tap(() => this.setMetaData()),
      map(({ data }) => data)
    )
  );

  private setMetaData(): void {
    const title = createRipostTitle('Galériák');
    const canonical = createCanonicalUrlForPageablePage('galeriak', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
  }
}
