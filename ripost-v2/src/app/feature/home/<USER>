import { inject } from '@angular/core';
import { ResolveFn, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { LayoutApiData, LayoutService } from '@trendency/kesma-ui';
import { catchError, map } from 'rxjs/operators';

export const homeResolver: ResolveFn<LayoutApiData> = (): Observable<LayoutApiData> => {
  const layoutService = inject(LayoutService);
  const router = inject(Router);

  return layoutService.getHomePage().pipe(
    catchError((err) => {
      router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
      return throwError(() => err);
    }),
    map(({ data }) => data)
  );
};
