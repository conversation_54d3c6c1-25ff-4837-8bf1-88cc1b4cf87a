@if (isBrowser) {
  <kesma-layout-editor [layoutComponentRef]="layoutComponent" [placeholderOverrides]="placeholderOverrides"></kesma-layout-editor>
  <ng-template
    #layoutComponent
    let-structure="structure"
    let-content="content"
    let-type="type"
    let-contentComponentsWrapper="contentComponentsWrapper"
    let-contentComponentsInnerWrapper="contentComponentsInnerWrapper"
    let-blockTitleWrapper="blockTitleWrapper"
  >
    <app-layout
      [layoutType]="type ?? LayoutPageType.HOME"
      [structure]="structure"
      [configuration]="content"
      [contentComponentsWrapper]="contentComponentsWrapper"
      [contentComponentsInnerWrapper]="contentComponentsInnerWrapper"
      [blockTitleWrapper]="blockTitleWrapper"
    ></app-layout>
  </ng-template>

  <ng-template #placeholderOverrides let-layoutElement="layoutElement" let-contentDisplay="contentDisplay">
    @switch (layoutElement.contentType) {
      @case (LayoutElementContentType.Ad) {
        <kesma-advertisement-placeholder [layoutElement]="layoutElement"></kesma-advertisement-placeholder>
      }
      @default {
        <ng-container [ngTemplateOutlet]="contentDisplay"></ng-container>
      }
    }
  </ng-template>
}
