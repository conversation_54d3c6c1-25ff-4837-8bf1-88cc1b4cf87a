import { Component, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LayoutElementContentConfiguration, LayoutElementRow, LayoutPageType } from '@trendency/kesma-ui';
import { LayoutComponent } from '../layout/components/layout/layout.component';

@Component({
  selector: 'app-layout-preview',
  templateUrl: './layout-preview.component.html',
  styleUrls: ['./layout-preview.component.scss'],
  imports: [LayoutComponent],
})
export class LayoutPreviewComponent implements OnInit {
  readonly adPageType = signal<string>('');
  readonly layoutApiData = signal<{
    struct: LayoutElementRow[];
    content: LayoutElementContentConfiguration[];
  }>(null!);

  private readonly route = inject(ActivatedRoute);

  readonly LayoutPageType = LayoutPageType;

  ngOnInit(): void {
    const { layoutData } = this.route.snapshot.data;
    const { pageType, column } = this.route.snapshot.queryParams;

    this.layoutApiData.set(layoutData);

    switch (pageType) {
      case 'home':
      case 'homepage':
        this.adPageType.set('main_page');
        break;
      case 'column':
        column && this.adPageType.set(`column_${column}`);
        break;
      case 'opinion':
        this.adPageType.set('opinion');
        break;
      case 'custombuiltpage':
        this.adPageType.set('all_articles_and_sub_pages');
    }
  }
}
