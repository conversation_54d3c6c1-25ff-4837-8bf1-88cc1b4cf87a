@if (vm(); as vm) {
  <kesma-layout
    [breakingNews]="vm.breakingNews"
    [configuration]="vm.configuration"
    [layoutType]="vm.layoutType"
    [structure]="vm.structure"
    [adPageType]="vm.adPageType"
    [blockTitleRef]="blockTitles"
    [contentComponentsRef]="contentComponents"
    [contentComponentWrapperRef]="contentComponentsWrapper()"
    [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper()"
    [blockTitleWrapperRef]="blockTitleWrapper()"
  >
  </kesma-layout>
}

<ng-template #blockTitles let-layoutType="layoutType" let-layoutElement="layoutElement"> !!! BLOCK TITLE !!!</ng-template>

<ng-template #contentComponents let-layoutElement="layoutElement" let-index="index" let-extractor="extractor" let-desktopWidth="desktopWidth">
  @if (layoutElement?.config || layoutElement?.configurable === false) {
    @switch (layoutElement.contentType) {
      @case (layoutElement.contentType === LayoutElementContentType.Ad) {
        @if (layoutElement.ad) {
          <kesma-advertisement-adocean
            [ad]="layoutElement.ad"
            [isHidden]="layoutElement.contentType !== LayoutElementContentType.Ad && !layoutElement.ad"
          ></kesma-advertisement-adocean>
        }
      }
      @case (LayoutElementContentType.Article) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-article-card
            [data]="data"
            [styleID]="layoutElement.styleId"
            [isSidebar]="layoutType() === LayoutPageType.SIDEBAR"
            [useColumnBackgroundColor]="layoutElement.useCategoryBackgroundColor"
            [desktopWidth]="desktopWidth"
            [class.category-background-color]="layoutElement.useCategoryBackgroundColor"
            [class.with-horizontal-separator]="layoutElement.withHorizontalSeparator"
          ></app-article-card>
        }
      }
      @case (LayoutElementContentType.Opinion) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-article-card [data]="data" [styleID]="layoutElement.styleId"></app-article-card>
        }
      }
      @case (LayoutElementContentType.PrBlock) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-article-card [data]="data" [styleID]="layoutElement.styleId"></app-article-card>
        }
      }
      @case (LayoutElementContentType.Gallery) {
        @if (index === 0 && layoutElement.extractorData; as data) {
          <app-gallery-list [data]="data"></app-gallery-list>
        }
      }
      @case (LayoutElementContentType.Wysiwyg) {
        @if (layoutElement.extractorData; as data) {
          <app-wysiwyg-box [htmlArray]="data"></app-wysiwyg-box>
        }
      }
      @case (LayoutElementContentType.Dossier) {
        @if (layoutElement.extractorData; as data) {
          <app-dossier-card [data]="data"></app-dossier-card>
        }
      }
      @case (LayoutElementContentType.Image) {
        @if (layoutElement.extractorData; as data) {
          <app-image [data]="data"></app-image>
        }
      }
      @case (LayoutElementContentType.Vote) {
        @if (layoutElement.extractorData; as extractorData) {
          @if ((voteCache[layoutElement?.details?.[0]?.value?.id ?? ''] | async) || extractorData; as voteData) {
            <app-voting [data]="voteData.data" [voteId]="voteData.votedId" (vote)="onVotingSubmit($event, voteData)" />
          }
        }
      }
      @case (LayoutElementContentType.Breaking) {
        @if (layoutElement.config?.selectedBreakings?.[index]; as data) {
          <app-breaking-card [data]="data"></app-breaking-card>
        }
      }
      @case (LayoutElementContentType.HtmlEmbed) {
        @if (index === 0 && layoutElement?.config?.htmlContent; as data) {
          <kesma-html-embed [data]="data"></kesma-html-embed>
        }
      }
      @case (LayoutElementContentType.FreshBlock) {
        @if (layoutElement.extractorData?.[index]; as data) {
          <app-fresh-article-card [data]="data"></app-fresh-article-card>
        }
      }
      @case (LayoutElementContentType.Quiz) {
        @if (layoutElement.extractorData; as data) {
          <app-quiz [data]="data"></app-quiz>
        }
      }
      @case (LayoutElementContentType.BrandingBoxEx) {
        <app-branding-box-ex [brand]="layoutElement.brand"></app-branding-box-ex>
      }
      @case (LayoutElementContentType.LATEST_ARTICLES) {
        <app-latest-articles></app-latest-articles>
      }
      @case (LayoutElementContentType.IngatlanbazarSearch) {
        <kesma-real-estate-bazaar-search-block
          [showBudapestLocations]="layoutElement.config.showBudapestLocations"
          [showCountyLocations]="layoutElement.config.showCountyLocations"
          [showCountyLocationsWithBudapest]="layoutElement.config.showCountyLocationsWithBudapest"
          [showOtherLocations]="layoutElement.config.showOtherLocations"
          [showNewBuildButton]="layoutElement.config.showNewBuildButton"
          [showAdvertiseButton]="layoutElement.config.showAdvertiseButton"
          [defaultLocation]="layoutElement.config.defaultLocation"
          [defaultType]="layoutElement.config.defaultType"
          [utmSource]="layoutElement.config.utmSource"
        >
        </kesma-real-estate-bazaar-search-block>
      }
    }
  }
  @if (layoutElement.contentType === LayoutElementContentType.IngatlanbazarConfigurable) {
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [showHeader]="layoutElement.showHeader"
      [itemsToShow]="layoutElement.itemsToShow"
      [data]="vm().realEstateData"
    >
    </kesma-real-estate-bazaar-block>
  }
  @if (layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR) {
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [showHeader]="true"
      [itemsToShow]="2"
      [data]="vm().realEstateData"
    ></kesma-real-estate-bazaar-block>
  }
</ng-template>
