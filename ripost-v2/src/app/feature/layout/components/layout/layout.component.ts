import { ChangeDetectionStrategy, Component, computed, inject, input, signal, TemplateRef, ViewChild } from '@angular/core';
import {
  AdvertisementAdoceanComponent,
  BlockWrapperTemplateData,
  BreakingNews,
  HtmlEmbedComponent,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentItemWrapperTemplateData,
  LayoutContentParams,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutPageType,
  mapRealEstateApiDataToRealEstateData,
  RealEstateBazaarApiData,
  RealEstateBazaarBackendResponse,
  RealEstateBazaarBlockComponent,
  RealEstateBazaarData,
  RealEstateBazaarSearchBlockComponent,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import {
  ArticleCardComponent,
  ArticleCardType,
  BrandingBoxExComponent,
  BreakingCardComponent,
  CleanHttpService,
  DossierCardComponent,
  FreshArticleCardComponent,
  GalleryListComponent,
  ImageComponent,
  VotingComponent,
  WysiwygBoxComponent,
} from '../../../../shared';
import { UtilService } from '@trendency/kesma-core';
import { LatestArticlesComponent } from '../../../../shared/components/latest-articles/latest-articles.component';
import { QuizComponent } from '../../../../shared/components/quiz/quiz.component';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  imports: [
    RealEstateBazaarBlockComponent,
    RealEstateBazaarSearchBlockComponent,
    HtmlEmbedComponent,
    AdvertisementAdoceanComponent,
    KesmaLayoutComponent,
    ArticleCardComponent,
    GalleryListComponent,
    WysiwygBoxComponent,
    DossierCardComponent,
    ImageComponent,
    VotingComponent,
    BreakingCardComponent,
    FreshArticleCardComponent,
    BrandingBoxExComponent,
    LatestArticlesComponent,
    QuizComponent,
    AsyncPipe,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutComponent {
  private readonly httpClient = inject(CleanHttpService);
  private readonly utilService = inject(UtilService);
  private readonly voteService = inject(VoteService);

  adPageType = input('');
  structure = input.required<LayoutElementRow[]>();
  configuration = input.required<LayoutElementContentConfiguration[]>();
  layoutType = input.required<LayoutPageType>();
  breakingNews = input<BreakingNews[]>([]);
  contentComponentsWrapper = input<TemplateRef<LayoutContentItemWrapperTemplateData>>();
  contentComponentsInnerWrapper = input<TemplateRef<LayoutContentItemWrapperTemplateData>>();
  blockTitleWrapper = input<TemplateRef<BlockWrapperTemplateData>>();

  vm = computed(() => {
    return {
      adPageType: this.adPageType(),
      structure: this.structure(),
      configuration: this.configuration(),
      layoutType: this.layoutType(),
      breakingNews: this.breakingNews(),
      realEstateData: this.realEstateData(),
      realEstateDataLoading: this.realEstateDataLoading(),
    };
  });

  @ViewChild('contentComponents', {
    read: TemplateRef,
    static: false,
  })
  contentComponents: TemplateRef<LayoutContentParams>;

  readonly LayoutElementContentType = LayoutElementContentType;
  readonly voteCache = this.voteService.voteCache;

  private readonly realEstateData = signal<RealEstateBazaarData[]>([]);
  private readonly realEstateDataLoading = signal(false);

  handleRealEstateInitEvent(): void {
    if (this.utilService.isBrowser() && !this.realEstateDataLoading() && this.realEstateData().length < 1) {
      return this.getRealEstateData();
    }
  }

  getRealEstateData(): void {
    this.realEstateDataLoading.set(true);
    const realEstates: Array<RealEstateBazaarData> = [];
    this.httpClient
      .get(
        // eslint-disable-next-line max-len
        'https://www.ingatlanbazar.hu/api/property-search?property_location=6,1000000004,1000000005,1000000006,1000000007&amp;;property_newbuildonly=on&amp;property__2=3_2'
      )
      .subscribe((data: RealEstateBazaarBackendResponse) => {
        data?.hits?.forEach((realEstate: RealEstateBazaarApiData) => {
          realEstates.push(mapRealEstateApiDataToRealEstateData(realEstate));
        });
        this.realEstateData.set(realEstates);
        this.realEstateDataLoading.set(false);
      });
  }

  onVotingSubmit(votedId: string, voteData: VoteDataWithAnswer): void {
    this.voteService.onVotingSubmit(votedId, voteData).subscribe();
  }

  protected readonly ArticleCardType = ArticleCardType;
  protected readonly LayoutPageType = LayoutPageType;
}
