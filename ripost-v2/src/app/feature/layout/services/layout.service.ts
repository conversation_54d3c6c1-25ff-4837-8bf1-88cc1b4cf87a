import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiResult, Layout, LayoutContent, LayoutWithExcludeIds, SelectedArticle } from '@trendency/kesma-ui';
import { ApiService } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class LayoutService {
  private readonly apiService = inject(ApiService);
  private readonly router = inject(Router);

  getLayoutWithExcludeIds(slug: string): Observable<LayoutWithExcludeIds> {
    return this.apiService.getCategoryLayout(slug).pipe(
      catchError((error) => {
        this.router
          .navigate(['/', '404'], {
            state: { errorResponse: JSON.stringify(error) },
            skipLocationChange: true,
          })
          .then();
        return throwError(() => error);
      }),
      map((layoutApiResponse: ApiResult<Layout>) => {
        const data = layoutApiResponse?.data?.content && layoutApiResponse?.data?.struct ? layoutApiResponse?.data : null;
        if (!data) {
          const defaultLayoutResponse: LayoutWithExcludeIds = {
            data: null,
            excludedIds: [],
            columnTitle: '',
          };

          return defaultLayoutResponse;
        }

        const excludedIds = data?.content?.reduce(this.collectExcludedIds.bind(this), []);
        const assignedLayoutResponse: LayoutWithExcludeIds = {
          data: data,
          excludedIds: excludedIds,
          columnTitle: layoutApiResponse?.meta?.['columnTitle'],
        };

        return assignedLayoutResponse;
      })
    );
  }

  private collectExcludedIds(total: string[], content: LayoutContent): string[] {
    const selectedArticles = this.setExcludedIds(content?.selectedArticles);
    const selectedOpinions = this.setExcludedIds(content?.selectedOpinions);

    return total?.concat(selectedArticles)?.concat(selectedOpinions);
  }

  private setExcludedIds(ids: SelectedArticle[]): string[] {
    if (!ids) {
      return [];
    }
    return ids?.map((data) => {
      return data?.id;
    });
  }
}
