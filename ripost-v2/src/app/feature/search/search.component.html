<div class="wrapper">
  <h1 class="page-title"><PERSON><PERSON><PERSON><PERSON></h1>
  
  @if (globalFilter()) {
    <p class="search-query">Ke<PERSON>ett kifejez<PERSON>: "{{ globalFilter() }}"</p>
  }

  @if (limitable()?.rowAllCount) {
    <p class="search-results-count">{{ limitable()?.rowAllCount }} találat</p>
  }

  @if (adverts()?.desktop?.leaderboard_1; as ad) {
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  }

  @if (adverts()?.desktop?.roadblock_1; as ad) {
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  }
  @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  }

  <div class="search-results">
    @if (articles().length > 0) {
      @for (article of articles(); track article.id; let i = $index) {
        <app-article-card [data]="article"></app-article-card>
        
        @if (i === 4) {
          @if (adverts()?.desktop?.roadblock_2; as ad) {
            <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          }
          @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
            <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          }
        }
      }
    } @else {
      <p class="no-results">Nincs találat a keresett kifejezésre.</p>
    }
  </div>
</div>
