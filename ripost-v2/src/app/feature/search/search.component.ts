import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, Signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, ArticleCard, LimitableMeta } from '@trendency/kesma-ui';
import { map, switchMap } from 'rxjs/operators';
import { ArticleCardComponent } from '../../shared';
import { SearchData } from './search.definitions';

@Component({
  selector: 'app-search',
  imports: [ArticleCardComponent, AdvertisementAdoceanComponent],
  templateUrl: './search.component.html',
  styleUrl: './search.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as SearchData)));
  readonly articles: Signal<ArticleCard[]> = computed(() => this.resolverData()?.articles ?? []);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.limitable);
  readonly globalFilter = computed(() => this.resolverData()?.globalFilter);

  readonly adverts = toSignal(
    this.route.data.pipe(
      switchMap(() => this.adStoreAdo.advertisemenets$),
      map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads)),
      takeUntilDestroyed(this.destroyRef)
    )
  );
}
