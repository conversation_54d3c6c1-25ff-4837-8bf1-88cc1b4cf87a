import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { Observable, of } from 'rxjs';
import { SearchData } from './search.definitions';

export const searchResolver: ResolveFn<SearchData> = (
  route: ActivatedRouteSnapshot
): Observable<SearchData> => {
  // TODO: Implement actual search API call
  // For now, return empty data
  return of({
    articles: [],
    limitable: {
      rowAllCount: 0,
      rowCount: 0,
      pageCount: 0,
      currentPage: 1,
    },
    globalFilter: route.queryParams['global_filter'] || '',
  });
};
