@if (!currentStaticPageType() || currentStaticPageType() === customStaticPageType.StaticPage) {
  <section class="static-page">
    <div class="with-sidebar">
      <div class="static-page-container">
        <div class="heading-line">
          <h4 class="title">{{ title() }}</h4>
        </div>
        @for (element of body(); track element.uuid) {
          @switch (element.type) {
            @case (ArticleBodyType.Wysywyg) {
              @for (item of element?.details; track item.uuid) {
                <app-wysiwyg-box [html]="item?.value"></app-wysiwyg-box>
              }
            }
          }
        }
      </div>
      <aside>
        <app-sidebar [adPageType]="adPageType"></app-sidebar>
      </aside>
    </div>
  </section>
}
@if (currentStaticPageType() === customStaticPageType.CustomPage) {
  <div class="wrapper">
    <div class="static-page-container-no-side">
      <app-layout [adPageType]="adPageType" [layoutType]="LayoutPageType.HOME" [structure]="layoutApiData()?.struct" [configuration]="layoutApiData()?.content">
      </app-layout>
    </div>
  </div>
}
