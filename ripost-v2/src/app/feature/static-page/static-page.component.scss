@use 'shared' as *;

/* .static-page {
  margin-bottom: 40px;

  .with-sidebar {
    display: flex;
    width: 1400px;
    margin: 0 auto;
    max-width: calc(100% - 30px);
    justify-content: space-between;

    .heading-line {
      margin: 40px 0 30px;
    }
  }

  .static-page-container {
    width: 920px;

    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }

  aside {
    display: block;
    min-width: 440px;
    max-width: 440px;
    margin-bottom: 30px;
    margin-left: 40px;

    @include media-breakpoint-down(md) {
      display: none;
    }
  }
} */
