import { AfterViewInit, ChangeDetectionStrategy, Component, computed, inject, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { EmbeddingService, SeoService } from '@trendency/kesma-core';
import { CustomStaticPageType, IComponentData, IStaticPageResponse } from './static-page.definitions';
import { AnalyticsService, ApiResponseMeta, ArticleBodyType, createCanonicalUrlForPageablePage, LayoutApiData, LayoutPageType } from '@trendency/kesma-ui';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { createRipostTitle, defaultMetaInfo, WysiwygBoxComponent } from '../../shared';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-static-page',
  templateUrl: './static-page.component.html',
  styleUrls: ['./static-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [WysiwygBoxComponent, SidebarComponent, LayoutComponent],
})
export class StaticPageComponent implements AfterViewInit {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly embedding = inject(EmbeddingService);
  private readonly analyticsService = inject(AnalyticsService);

  readonly LayoutPageType = LayoutPageType;
  readonly adPageType = 'all_articles_and_sub_pages';
  readonly ArticleBodyType = ArticleBodyType;
  readonly customStaticPageType = CustomStaticPageType;

  readonly resolverData = toSignal(
    this.route.data.pipe(
      map(({ data }) => data as { meta: ApiResponseMeta; data: IStaticPageResponse | LayoutApiData }),
      takeUntilDestroyed()
    )
  );

  readonly currentStaticPageType: Signal<CustomStaticPageType | undefined> = computed(() => this.resolverData()?.meta?.['customStaticPageType']);

  readonly staticPageResponse: Signal<IStaticPageResponse | undefined> = computed(() =>
    this.currentStaticPageType() === CustomStaticPageType.StaticPage ? (this.resolverData()?.data as IStaticPageResponse) : undefined
  );

  readonly layoutApiData: Signal<LayoutApiData | undefined> = computed(() =>
    this.currentStaticPageType() === CustomStaticPageType.CustomPage ? (this.resolverData()?.data as LayoutApiData) : undefined
  );

  readonly title: Signal<string> = computed(() =>
    this.currentStaticPageType() === CustomStaticPageType.CustomPage
      ? (this.resolverData()?.meta?.['customStaticPageType'] ?? '')
      : (this.staticPageResponse()?.title ?? '')
  );

  readonly body: Signal<IComponentData[]> = computed(() => this.staticPageResponse()?.body ?? []);

  constructor() {
    const canonical = createCanonicalUrlForPageablePage('', this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);

    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: createRipostTitle(this.title()),
      ogTitle: this.title(),
    });

    this.analyticsService.sendPageView(undefined, this.currentStaticPageType() === CustomStaticPageType.CustomPage ? 'Egyedi oldal' : 'Statikus oldal');
  }

  ngAfterViewInit(): void {
    this.embedding.loadEmbedMedia();
  }
}
