import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ReqService } from '@trendency/kesma-core';
import { ApiResponseMeta, ApiResult, LayoutApiData, LayoutMeta, StaticPageResponse } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class StaticPageService {
  private readonly reqService = inject(ReqService);

  getStaticPage(slug: string): Observable<ApiResult<LayoutApiData, LayoutMeta> | ApiResult<StaticPageResponse, ApiResponseMeta>> {
    return this.reqService.get(`/custom-static-page-by-slug/${slug}`);
  }

  getStaticPagePreview(slug: string, previewHash: string): Observable<ApiResult<LayoutApiData, LayoutMeta> | ApiResult<StaticPageResponse, ApiResponseMeta>> {
    return this.reqService.get(`/content-page/static-page/${slug}/preview/view`, { params: { previewHash } });
  }
}
