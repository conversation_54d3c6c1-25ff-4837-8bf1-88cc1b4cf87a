import { ChangeDetectionStrategy, Component, computed, inject, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { ArticleCard, Tag } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../../shared';
import { map } from 'rxjs/operators';
import { TagsPageResponse } from './tags-page.resolver';

@Component({
  selector: 'app-tags-page',
  imports: [ArticleCardComponent],
  templateUrl: './tags-page.component.html',
  styleUrl: './tags-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagsPageComponent {
  private readonly route = inject(ActivatedRoute);

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as TagsPageResponse)));

  readonly tag: Signal<Tag | undefined> = computed(() => this.resolverData()?.tag);
  readonly articles: Signal<ArticleCard[] | undefined> = computed(() => this.resolverData()?.articles);
}
