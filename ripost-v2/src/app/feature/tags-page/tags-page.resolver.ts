import { ArticleCard, LimitableMeta, RedirectService, Tag } from '@trendency/kesma-ui';
import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { TagsPageService } from './tags-page.service';
import { forkJoin, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { searchResultToArticleCard } from '../../shared';

export type TagsPageResponse = {
  readonly tag: Tag;
  readonly articles: ArticleCard[];
  readonly limitable: LimitableMeta;
};

const MAX_RESULTS_PER_PAGE = 20;

export const tagsPageResolver: ResolveFn<TagsPageResponse> = (route: ActivatedRouteSnapshot) => {
  const router = inject(Router);
  const tagsPageService = inject(TagsPageService);
  const redirectService = inject(RedirectService);
  const slug = route.params['tag'];
  const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;

  const tag$ = tagsPageService.getTag(slug);
  const articles$ = tagsPageService.searchArticleByTags([slug], currentPage, MAX_RESULTS_PER_PAGE);

  return forkJoin([tag$, articles$]).pipe(
    tap(([, articleResponse]) => {
      const meta = articleResponse.meta;

      if (redirectService.shouldBeRedirect(currentPage, articleResponse?.data)) {
        redirectService.redirectOldUrl(`/cimke/${slug}`, false, 302);
      }

      // if tag is "merged tag"
      if (meta?.['redirect']?.tag?.slug) {
        redirectService.redirectOldUrl(`cimke/${meta?.['redirect'].tag.slug}`);
      }
    }),
    map(([tagResponse, articleResponse]) => {
      return {
        tag: tagResponse?.data,
        articles: articleResponse?.data?.map(searchResultToArticleCard),
        limitable: articleResponse?.meta?.limitable,
      };
    }),
    catchError((err) => {
      router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
      return throwError(() => err);
    })
  );
};
