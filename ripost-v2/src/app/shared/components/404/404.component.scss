@use 'shared' as *;

:host {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 32px;

  @include media-breakpoint-down(md) {
    padding: 24px 16px;
  }

  .title-img {
    @include media-breakpoint-down(md) {
      max-height: 80px;
    }
  }

  .not-found {
    text-align: center;
    font-family: var(--kui-font-secondary);
    font-size: 30px;
    font-style: normal;
    font-weight: 700;
    line-height: 34px;
    letter-spacing: -0.3px;

    @include media-breakpoint-down(md) {
      font-size: 26px;
      line-height: 30px;
      letter-spacing: -0.26px;
    }
  }

  .description {
    text-align: center;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    letter-spacing: -0.18px;

    @include media-breakpoint-down(md) {
      font-size: 16px;
      line-height: 24px;
      letter-spacing: -0.16px;
    }
  }
}
