import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { defaultMetaInfo } from '../../constants';
import { createRipostTitle } from '../../utils';
import { SimpleButtonComponent } from '../simple-button/simple-button.component';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-404',
  templateUrl: './404.component.html',
  styleUrls: ['./404.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SimpleButtonComponent, RouterLink],
})
export class Error404Component implements OnInit {
  private readonly seo = inject(SeoService);
  private readonly utilsService = inject(UtilService);
  private readonly response = inject(RESPONSE, { optional: true });

  ngOnInit(): void {
    const title = createRipostTitle('404 - Az oldal nem található');
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      robots: 'noindex, follow',
    });
    if (!this.utilsService.isBrowser() && this.response) {
      this.response.status(404);
    }
  }
}
