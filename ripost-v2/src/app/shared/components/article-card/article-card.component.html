@if (data) {
  <article class="article" [ngClass]="{ small: desktopWidth() <= 3 }">
    @switch (styleId) {
      @case (ArticleCardType.MainArticle) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showLabel: true, withRecommendedTitle: true, withPreTitle: true }"></ng-container>
      }
      @case (ArticleCardType.LatestArticles) {
        <ng-content></ng-content>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate"></ng-container>
      }
      @case (ArticleCardType.TopImgColumnTitleLead) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate; context: { showLabel: true }"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showLead: true }"></ng-container>
      }
      @case (ArticleCardType.TopImgColumnTitleLeadPadding) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate; context: { showLabel: true }"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showLead: true, withTitleBackground: true }"></ng-container>
      }
      @case (ArticleCardType.TopImgColumnTitle) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate; context: { showLabel: true }"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate"></ng-container>
      }
      @case (ArticleCardType.TopImgColumnTitlePadding) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate; context: { showLabel: true }"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { withTitleBackground: true }"></ng-container>
      }
      @case (ArticleCardType.BigPictureColumnTitle) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showLabel: true }"></ng-container>
      }
      @case (ArticleCardType.SideImgColumnTitleLeadPublished) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate; context: { showLabel: true }"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showLead: true, showDate: true }"></ng-container>
      }
      @case (ArticleCardType.SideImgColumnTitleLead) {
        <ng-container *ngTemplateOutlet="ArticleThumbnailTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="ArticleContentTemplate; context: { showLabel: true, showLead: true }"></ng-container>
      }
    }
  </article>
}

<ng-template
  #ArticleContentTemplate
  let-withRecommendedTitle="withRecommendedTitle"
  let-withTitleBackground="withTitleBackground"
  let-withPreTitle="withPreTitle"
  let-showDate="showDate"
  let-showLabel="showLabel"
  let-showLead="showLead"
>
  <div class="article-data">
    @if (showLabel) {
      <ng-container *ngTemplateOutlet="ArticleDisplayedColumnTemplate"></ng-container>
    }
    @if (showDate) {
      <div class="article-published-date">{{ data?.publishDate | publishedAgo: 'yyyy. MM. dd.' }}</div>
    }
    <div class="article-title-wrapper">
      @if (withPreTitle && data?.preTitle) {
        <a class="article-pre-title" [routerLink]="articleLink">{{ data?.preTitle }}</a>
      }
      <a [routerLink]="articleLink" class="article-link" [class.title-background]="withTitleBackground">
        <h2 class="title">
          {{ data?.title }}
        </h2>
      </a>
      @if (withRecommendedTitle && data?.recommendedTitle) {
        <a class="article-recommended-title" [routerLink]="articleLink">{{ data?.recommendedTitle }}</a>
      }
    </div>
    @if (showLead) {
      <a [routerLink]="articleLink" class="article-lead">{{ data?.lead }}</a>
    }
  </div>
</ng-template>

<ng-template #ArticleThumbnailTemplate let-displayedAspectRatio="displayedAspectRatio" let-showLabel="showLabel">
  <a class="thumbnail-wrapper" [class.with-label]="showLabel" [routerLink]="articleLink">
    @if (showAdultLayer()) {
      <kesma-adult-overlay>
        <ng-container *ngTemplateOutlet="Image"></ng-container>
      </kesma-adult-overlay>
    } @else {
      <ng-container *ngTemplateOutlet="Image"></ng-container>
    }
    <ng-template #Image>
      <img
        class="thumbnail"
        [class.is-placeholder]=""
        withFocusPoint
        [data]="data?.thumbnailFocusedImages"
        [displayedUrl]="displayedThumbnailUrl"
        [displayedAspectRatio]="displayedAspectRatio"
        [alt]="data?.thumbnail?.alt ?? data?.title"
        [attr.loading]="useEagerLoad() ? 'eager' : 'lazy'"
        [attr.fetchpriority]="fetchPriority()"
      />
      @if (showLabel) {
        <ng-container *ngTemplateOutlet="ArticleDisplayedColumnTemplate"></ng-container>
      }
    </ng-template>
  </a>
</ng-template>

<ng-template #ArticleBadgesTemplate>
  @if (hasBadges) {
    <div class="badges">
      @if (toBool(data?.isAdultsOnly)) {
        <div class="with-text">18+</div>
      }
      @if (toBool(data?.isVideoType)) {
        <div class="with-icon">
          <kesma-icon [name]="'video'" [size]="desktopWidth() <= 3 ? 15 : 20"></kesma-icon>
        </div>
      }
      @if (toBool(data?.hasGallery)) {
        <div class="with-icon">
          <kesma-icon [name]="'gallery'" [size]="desktopWidth() <= 3 ? 15 : 20"></kesma-icon>
        </div>
      }
      @if (toBool(data?.isPodcastType)) {
        <div class="with-icon">
          <kesma-icon [name]="'podcast'" [size]="desktopWidth() <= 3 ? 15 : 20"></kesma-icon>
        </div>
      }
    </div>
  }
</ng-template>

<ng-template #ArticleDisplayedColumnTemplate>
  <a class="article-label" [ngClass]="{ small: desktopWidth() <= 3 }" [routerLink]="['/', 'rovat', data?.category?.slug]">
    <div class="left-block"></div>
    <div class="label-text">{{ data?.category?.name }}</div>
    <div class="badge-list">
      <ng-container *ngTemplateOutlet="ArticleBadgesTemplate"></ng-container>
    </div>
  </a>
</ng-template>
