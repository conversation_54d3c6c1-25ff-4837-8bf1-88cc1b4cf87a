@use 'shared' as *;
@mixin small-label {
  .article-label {
    .left-block {
      height: 24px;
    }

    .label-text {
      font-size: 15px;
      line-height: 15px;
      letter-spacing: -0.15px;
      padding: 0 4px;
      height: 24px;
    }

    .badge-list {
      height: 24px;

      .badges {
        .with-icon,
        .with-text {
          height: 100%;
          font-size: 15px;
        }
      }
    }
  }
}

@mixin list-card-small {
  .article {
    &-label {
      bottom: 10px;
      left: 10px;
    }

    &-published-date {
      font-size: 18px;
      letter-spacing: -0.18px;
    }

    &-title-wrapper {
      .article-link {
        h2 {
          font-size: 28px;
          font-weight: 700;
          line-height: 32px;
        }
      }
    }

    &-lead {
      font-size: 16px;
      line-height: 22px;
      letter-spacing: -0.16px;
    }
  }
}
@mixin with-title-background {
  .article-title-wrapper {
    .article-link {
      display: block;

      h2 {
        display: inline;
        width: fit-content;
        color: var(--kui-white);
        background-color: var(--kui-black);
        padding: 0 4px;
        box-decoration-break: clone;
        -webkit-box-decoration-break: clone;
      }
    }
  }
}

:host {
  --theme-color: var(--kui-red-500);
  --border-color: color-mix(in oklab, var(--tag-color), 30% var(--kui-black));
  width: 100%;
  position: relative;

  .thumbnail-wrapper {
    display: inline-block;
    width: 100%;
    height: 100%;
    &.with-label {
      position: relative;
    }
  }
  .article {
    &-label {
      max-width: max-content;
      display: flex;
      align-items: center;
      gap: 2px;

      .left-block {
        height: 40px;
        background-color: var(--tag-color);
        transform: skew(-12deg);
        width: 2px;
      }

      .label-text {
        background-color: var(--tag-color);
        display: flex;
        align-items: center;
        color: var(--kui-white);
        text-align: center;
        font-size: 24px;
        font-weight: 500;
        line-height: 25px;
        letter-spacing: -0.15px;
        text-transform: uppercase;
        padding: 0 7px;
        transform: skew(-12deg);
        height: 40px;
        font-family: var(--kui-font-secondary);
      }

      .badge-list {
        .badges {
          height: 100%;
          display: flex;
          gap: 2px;
          align-items: center;

          .with-icon,
          .with-text {
            background-color: var(--tag-color);
            color: var(--kui-white);
            display: flex;
            transform: skew(-12deg);
            padding: 0 4px;
            font-size: 20px;
            font-family: var(--kui-font-secondary);
            height: 40px;
            align-items: center;

            kesma-icon {
              transform: skew(12deg);
            }
          }
        }
      }
    }
    &.small {
      @include small-label;
    }

    @include media-breakpoint-down(sm) {
      @include small-label;
    }
  }

  &.with-horizontal-separator {
    border-color: var(--tag-color);
    border-bottom: 4px solid var(--border-color);
  }

  &.style-MainArticle {
    .thumbnail-wrapper {
      img {
        width: 100%;
      }
    }

    .article {
      &-label {
        position: absolute;
        top: 40px;
        left: 24px;

        @include media-breakpoint-down(sm) {
          top: 16px;
          left: 16px;
        }
      }

      &-title-wrapper {
        position: absolute;
        bottom: 40px;
        left: 24px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        width: calc(100% - 48px);
        height: calc(100% - 80px);
        justify-content: flex-end;
        overflow: hidden;

        @include media-breakpoint-down(sm) {
          left: 16px;
          bottom: 16px;
          width: calc(100% - 32px);
          height: calc(100% - 32px);
        }

        .article-pre-title {
          font-family: var(--kui-secondary);
          font-size: 18px;
          font-weight: 500;
          line-height: 18.333px;
          letter-spacing: -0.18px;
          padding: 5px;
          color: white;
          background-color: var(--kui-red-500);
          transform: skew(-12deg);
          width: max-content;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .article-link {
          display: block;

          h2 {
            display: inline;
            width: fit-content;
            color: var(--kui-black);
            background-color: var(--kui-white);
            font-family: var(--kui-font-secondary);
            font-size: 38px;
            font-weight: 700;
            line-height: 51px;
            letter-spacing: -0.38px;
            text-transform: uppercase;
            padding: 0 4px;
            box-decoration-break: clone;
            -webkit-box-decoration-break: clone;
          }
        }

        .article-recommended-title {
          background-color: var(--kui-yellow-100);
          color: var(--kui-black);
          font-family: var(--kui-font-secondary);
          font-size: 18px;
          font-weight: 500;
          line-height: 20px;
          padding: 8px;
          display: inline;
          width: max-content;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  &.style-LatestArticles {
    .article-link {
      color: var(--kui-black);
    }
    .title {
      font-family: var(--kui-font-secondary);
      font-size: 18px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px;
      letter-spacing: -0.18px;
      padding-left: 4px;
    }
  }

  &.style-TopImgColumnTitleLead,
  &.style-TopImgColumnTitleLeadPadding,
  &.style-TopImgColumnTitle,
  &.style-TopImgColumnTitlePadding {
    .thumbnail-wrapper {
      display: block;

      img {
        width: 100%;
      }
    }

    .article {
      &-data {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding-top: 4px;
      }

      &-label {
        position: absolute;
        bottom: 10px;
        left: 10px;

        @include media-breakpoint-down(sm) {
          bottom: 6px;
          left: 6px;
        }
      }

      &-title-wrapper {
        .article-link {
          padding-top: 4px;
          h2 {
            color: var(--kui-black);
            font-family: var(--kui-font-secondary);
            font-size: 28px;
            font-weight: 700;
            line-height: 32px;
            letter-spacing: -0.28px;
            text-transform: uppercase;

            @include media-breakpoint-down(sm) {
              font-size: 18px;
              line-height: 20px;
              letter-spacing: -0.18px;
            }
          }
        }
      }

      &-lead {
        color: var(--kui-black);
        font-size: 18px;
        font-weight: 400;
        line-height: 26px;

        @include media-breakpoint-down(sm) {
          font-size: 11px;
          line-height: 16px;
        }
      }
    }
  }

  &.style-TopImgColumnTitleLeadPadding {
    &.with-title-background {
      @include with-title-background;
    }

    &.category-background-color {
      @include with-title-background;
      .article-data {
        background-color: var(--tag-color);
      }
    }
    .article-data {
      padding: 8px 10px 16px 10px;
    }
  }

  &.style-TopImgColumnTitlePadding {
    .article-data {
      padding: 8px 10px 16px 10px;
    }
  }

  &.style-BigPictureColumnTitle {
    .thumbnail-wrapper {
      img {
        width: 100%;
      }
    }
    .article {
      &-label {
        padding-bottom: 14px;
        @include media-breakpoint-down(sm) {
          padding-bottom: 12px;
        }
      }
      &-data {
        position: absolute;
        bottom: 16px;
        left: 16px;
        width: calc(100% - 32px);
        height: calc(100% - 60px);
        overflow: hidden;

        @include media-breakpoint-down(sm) {
          bottom: 8px;
          left: 8px;
          width: calc(100% - 16px);
          height: calc(100% - 32px);
        }

        .article-link {
          color: var(--kui-white);
          font-family: var(--kui-font-secondary);
          font-size: 38px;
          font-weight: 700;
          line-height: 42px;
          letter-spacing: -0.38px;
          text-transform: uppercase;

          @include media-breakpoint-down(sm) {
            font-size: 24px;
            line-height: 26px;
            letter-spacing: -0.24px;
          }
        }
      }
    }
  }

  &.style-SideImgColumnTitleLeadPublished,
  &.style-SideImgColumnTitleLead {
    article {
      display: flex;
      gap: 24px;
      flex-wrap: nowrap;
      @include media-breakpoint-down(sm) {
        gap: 8px;
        flex-wrap: wrap;
      }

      &.small {
        @include list-card-small;
      }
    }

    .thumbnail-wrapper {
      width: 50%;

      @include media-breakpoint-down(sm) {
        width: 100%;
      }

      img {
        width: 100%;
        height: 100%;
      }
    }

    .article {
      &-data {
        width: 50%;
        display: flex;
        flex-direction: column;
        gap: 8px;

        @include media-breakpoint-down(sm) {
          width: 100%;
        }
      }

      &-title-wrapper {
        .article-link {
          h2 {
            font-family: var(--kui-font-secondary);
            font-size: 46px;
            font-weight: 700;
            line-height: 48px;
            text-transform: uppercase;
            color: var(--kui-black);

            @include media-breakpoint-down(sm) {
              font-size: 35px;
              line-height: 38px;
              letter-spacing: -0.35px;
            }
          }
        }
      }

      &-lead {
        color: var(--kui-black);
        font-size: 18px;
        font-weight: 400;
        line-height: 26px;

        @include media-breakpoint-down(sm) {
          font-size: 16px;
          line-height: 24px;
          letter-spacing: -0.16px;
        }
      }
    }
  }

  &.style-SideImgColumnTitleLeadPublished {
    .article {
      &-label {
        position: absolute;
        bottom: 16px;
        left: 16px;

        @include media-breakpoint-down(sm) {
          bottom: 10px;
          left: 10px;
        }
      }

      &-published-date {
        font-family: var(--kui-font-secondary);
        font-size: 24px;
        font-weight: 500;
        line-height: 25px;
        letter-spacing: -0.24px;
        padding: 3px 6px;
        width: max-content;
        background-color: var(--kui-black-150);
        transform: skew(-12deg);

        @include media-breakpoint-down(sm) {
          font-size: 20px;
          line-height: 20px;
          letter-spacing: -0.2px;
        }
      }
    }
  }
}
