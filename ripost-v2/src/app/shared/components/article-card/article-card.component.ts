import { ChangeDetectionStrategy, Component, HostBinding, input, Input } from '@angular/core';
import { AdultOverlayComponent, ArticleCard, BaseComponent, buildArticleUrl, FocusPointDirective, IconComponent, toBool } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { PlaceholderImg } from '../../constants';
import { RouterLink } from '@angular/router';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import { FormatPipeModule } from 'ngx-date-fns';
import { PublishedAgoPipe } from '../../pipes';

@Component({
  selector: 'app-article-card',
  templateUrl: './article-card.component.html',
  styleUrl: './article-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgTemplateOutlet, AdultOverlayComponent, FocusPointDirective, IconComponent, FormatPipeModule, PublishedAgoPipe, NgClass],
})
export class ArticleCardComponent extends BaseComponent<ArticleCard> {
  @HostBinding('class') hostClass?: string;
  @HostBinding('style') get hostStyle(): string {
    return `${this.data?.columnMainColor ? `--tag-color: ${this.data?.columnMainColor}` : `--tag-color: var(--theme-color);`}`;
  }

  @Input() set styleID(styleID: ArticleCardType) {
    this.styleId = styleID;
    this.hostClass = `article-card style-${ArticleCardType[styleID]}`;
  }

  styleId?: ArticleCardType;
  displayedThumbnailUrl?: string;
  articleLink?: string[];

  desktopWidth = input<number>(12);
  isSidebar = input<boolean>(false);
  showAdultLayer = input<boolean>(false);
  hasLeftBorder = input<boolean>(false);
  fetchPriority = input<'high' | 'low' | 'auto'>('auto');
  useEagerLoad = input<boolean>(false);
  useColumnBackgroundColor = input<boolean>(false);

  protected override setProperties(): void {
    this.displayedThumbnailUrl = this.data?.thumbnail?.url || PlaceholderImg;
    this.articleLink = this.data && buildArticleUrl(this.data);
  }

  get hasBadges(): boolean {
    return [this.data?.isVideoType, this.data?.hasGallery, this.data?.isAdultsOnly, this.data?.isPodcastType].map(toBool).includes(true);
  }

  protected readonly ArticleCardType = ArticleCardType;
  protected readonly toBool = toBool;
}
