import { AsyncPipe, DOCUMENT } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterModule } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanStoreService,
  AdvertisementBannerName,
  AdvertisementsByMedium,
  BreakingBlock,
  InitResolverData,
  MediaworksFooterCompactComponent,
  PortfolioItem,
  PortfolioResponse,
  SimplifiedMenuItem,
  WINDOW,
} from '@trendency/kesma-ui';
import { combineLatest, interval, Observable, of, take } from 'rxjs';
import { delayWhen, filter, map, mergeMap, startWith } from 'rxjs/operators';
import { ApiService } from '../../services';
import { FooterComponent } from '../footer/footer.component';

declare let __tcfapi: (command: string, version?: number, callback?: (response: any, success: boolean) => void, param?: any) => void;

interface LayoutAds {
  readonly header?: AdvertisementsByMedium;
  readonly footer?: AdvertisementsByMedium;
}

@Component({
  selector: 'app-base',
  templateUrl: './base.component.html',
  styleUrls: ['./base.component.scss'],
  imports: [RouterModule, AsyncPipe, FooterComponent, MediaworksFooterCompactComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseComponent implements OnInit, AfterViewInit {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly utils = inject(UtilService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly apiService = inject(ApiService);
  private readonly documentElement = inject(DOCUMENT);
  private readonly window = inject(WINDOW);

  readonly mainMenu = signal<SimplifiedMenuItem[]>([]);
  readonly topMenu = signal<SimplifiedMenuItem[]>([]);
  readonly footer = signal<SimplifiedMenuItem[]>([]);

  readonly ads = signal<Record<string, Advertisement> | undefined>(undefined);
  readonly breakingNews = signal<BreakingBlock | undefined>(undefined);
  readonly isArticleUrl = signal<boolean>(false);

  readonly isAdblockerActive = signal<boolean>(false);

  readonly mediaworksFooter = signal<PortfolioItem[]>([]);

  isFullWidth$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => this.route.snapshot.firstChild?.data?.['isFullWidth'] === true)
  );

  ngOnInit(): void {
    // any is necessary due to missing overlap of `NavigationEnd` and result of `router.events`
    combineLatest([this.router.events as Observable<any | NavigationEnd>, this.adStoreAdo.isAdult.asObservable()])
      .pipe(
        filter<[any | NavigationEnd, boolean]>(([event]) => event instanceof NavigationEnd),
        startWith([null, false]),
        mergeMap(() => {
          if (!this.utils.isBrowser() || !this.documentElement?.location) {
            return of({
              header: {} as AdvertisementsByMedium,
              footer: {} as AdvertisementsByMedium,
            });
          }

          const [_, path1, path2] = this.documentElement?.location?.pathname.split('/') ?? ['', ''];

          this.isArticleUrl.set(!isNaN(parseInt(path2, 10)));

          if (this.isArticleUrl()) {
            this.adStoreAdo.currentMasterIdSubject.next('');
          }

          return this.adStoreAdo.advertisemenets$.pipe(
            map<Advertisement[], LayoutAds>((ads) => {
              const footerMediumSeparator = this.baseElementPageTypeSwitch(path1, 'footer');
              const headerMediumSeparator = this.baseElementPageTypeSwitch(path1, 'header');

              return {
                footer: this.adStoreAdo.separateAdsByMedium(ads, footerMediumSeparator.page, footerMediumSeparator.ads),
                header: this.adStoreAdo.separateAdsByMedium(ads, headerMediumSeparator.page, headerMediumSeparator.ads),
              };
            }),
            delayWhen((ads) =>
              this.isArticleUrl()
                ? (ads.header && this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.header.desktop?.leaderboard_1?.masterId) ||
                  (ads.header && this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.header.mobile?.leaderboard_1?.masterId)
                  ? interval(1000)
                  : interval(0)
                : interval(0)
            )
          );
        })
      )
      .subscribe((adverts: { header?: AdvertisementsByMedium; footer?: AdvertisementsByMedium }) => {
        this.ads.set({
          leaderboard_2_bottom: adverts?.footer?.desktop?.leaderboard_2 as Advertisement,
          leaderboard_1: adverts?.header?.desktop?.leaderboard_1 as Advertisement,
          layer: adverts?.header?.desktop?.layer as Advertisement,
          interstitial: adverts?.header?.desktop?.interstitial as Advertisement,
          interstitialMobile: adverts?.header?.mobile?.interstitial as Advertisement,
        });
        if (this.route.snapshot.firstChild?.data?.['isLeaderboardAdHidden']) {
          this.ads.set({});
        }
      });

    this.apiService
      .getPortfolioFooter()
      .pipe(take(1))
      .subscribe((data: PortfolioResponse) => {
        this.mediaworksFooter.set(data.data);
      });

    const responseData: InitResolverData = this.route.snapshot.data?.['data'] ?? {};
    this.breakingNews.set(responseData?.init?.breakingNews);

    const {
      menu: { header_0, header_1, footer },
    } = responseData || {};
    this.mainMenu.set(header_0 ?? []);
    this.topMenu.set(header_1 ?? []);
    this.footer.set(footer ?? []);
  }

  public ngAfterViewInit(): void {
    this.adblockerActiveStatus();
  }

  private baseElementPageTypeSwitch(path: string, pageType: 'header' | 'footer'): { page: string; ads: AdvertisementBannerName[] } {
    if (pageType === 'header') {
      switch (path) {
        case '':
          return { page: 'main_page', ads: ['leaderboard_1', 'layer', 'interstitial', 'leaderboard_2'] };
        default:
          return { page: 'all_articles_and_sub_pages', ads: ['leaderboard_1', 'layer', 'interstitial'] };
      }
    }

    switch (path) {
      case '':
        return { page: 'main_page', ads: ['leaderboard_3'] };
      default:
        return { page: 'all_articles_and_sub_pages', ads: ['leaderboard_2'] };
    }
  }

  private adblockerActiveStatus(): boolean | void {
    if (!this.utils.isBrowser()) {
      //Manually override to return false, because the ado does not exist on SSR.
      return;
    }
    return this.isAdblockerActive.set(typeof this.window?.ado !== 'object');
  }

  openCookieSettings(): void {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }
}
