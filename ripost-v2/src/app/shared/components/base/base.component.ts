import { AsyncPipe, DOCUMENT } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, NavigationEnd, Router, RouterModule } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementBannerName,
  AdvertisementsByMedium,
  ArticleCard,
  BreakingBlock,
  InitResolverData,
  MediaworksFooterCompactComponent,
  PAGE_TYPES,
  PortfolioItem,
  PortfolioResponse,
  SimplifiedMenuItem,
  WINDOW,
} from '@trendency/kesma-ui';
import { combineLatest, interval, Observable, of, take } from 'rxjs';
import { delayWhen, filter, map, mergeMap, startWith } from 'rxjs/operators';
import { ApiService, UrlService } from '../../services';
import { FooterComponent } from '../footer/footer.component';

declare let __tcfapi: (command: string, version?: number, callback?: (response: any, success: boolean) => void, param?: any) => void;

interface LayoutAds {
  readonly header?: AdvertisementsByMedium;
  readonly footer?: AdvertisementsByMedium;
}

@Component({
  selector: 'app-base',
  templateUrl: './base.component.html',
  styleUrls: ['./base.component.scss'],
  imports: [RouterModule, AsyncPipe, FooterComponent, MediaworksFooterCompactComponent, AdvertisementAdoceanComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseComponent implements OnInit, AfterViewInit {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly utils = inject(UtilService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly apiService = inject(ApiService);
  private readonly documentElement = inject(DOCUMENT);
  private readonly window = inject(WINDOW);
  private readonly urlService = inject(UrlService);
  private readonly destroyRef = inject(DestroyRef);

  readonly mainMenu = signal<SimplifiedMenuItem[]>([]);
  readonly topMenu = signal<SimplifiedMenuItem[]>([]);
  readonly footer = signal<SimplifiedMenuItem[]>([]);

  readonly ads = signal<Record<string, Advertisement> | undefined>(undefined);
  readonly breakingNews = signal<BreakingBlock | undefined>(undefined);
  readonly isArticleUrl = signal<boolean>(false);

  readonly isAdblockerActive = signal<boolean>(false);
  readonly recommendations = signal<Record<string, ArticleCard[]> | undefined>(undefined);
  readonly ads = signal<Record<string, Advertisement> | undefined>(undefined);
  readonly isArticleUrl = signal<boolean>(false);
  readonly adverts = signal<AdvertisementsByMedium | undefined>(undefined);
  readonly areAdsInitiated = signal<boolean>(false);

  readonly mediaworksFooter = signal<PortfolioItem[]>([]);

  isFullWidth$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => this.route.snapshot.firstChild?.data?.['isFullWidth'] === true)
  );

  ngOnInit(): void {
    this.loadPortfolioData();

    // any is necessary due to missing overlap of `NavigationEnd` and result of `router.events`
    combineLatest([this.router.events as Observable<any | NavigationEnd>, this.adStoreAdo.isAdult.asObservable()])
      .pipe(
        filter<[any | NavigationEnd, boolean]>(([event]) => event instanceof NavigationEnd),
        startWith([null, false]),
        mergeMap(([event]) => {
          if (!this.utils.isBrowser() || !this.documentElement?.location) {
            return of({} as AdvertisementsByMedium);
          }

          if (event?.url) {
            this.urlService.setPreviousUrl(event.url);
          }

          const [_, path1, path2] = this.documentElement?.location?.pathname.split('/') ?? ['', ''];

          this.isArticleUrl.set(!isNaN(parseInt(path2, 10)));

          if (this.isArticleUrl()) {
            this.adStoreAdo.currentMasterIdSubject.next('');
          }

          return this.adStoreAdo.advertisemenets$.pipe(
            map<Advertisement[], AdvertisementsByMedium>((ads) => {
              const headerMediumSeparator = this.baseElementPageTypeSwitch(path1);

              return this.adStoreAdo.separateAdsByMedium(ads, headerMediumSeparator.page);
            }),
            delayWhen((ads) =>
              this.isArticleUrl()
                ? this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.desktop?.leaderboard_1?.masterId ||
                  this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.mobile?.mobilrectangle_footer?.masterId
                  ? interval(1000)
                  : interval(0)
                : interval(0)
            )
          );
        }),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((adverts) => {
        this.adverts.set(adverts);
        this.areAdsInitiated.set(true);
      });

    const responseData: InitResolverData & {
      recommendations: Record<string, ArticleCard[]> | undefined;
    } = this.route.snapshot.data?.['data'] ?? {};
    this.breakingNews.set(responseData?.init?.breakingNews);

    const {
      menu: { header_0, header_1, footer },
    } = responseData || {};
    this.mainMenu.set(header_0 ?? []);
    this.topMenu.set(header_1 ?? []);
    this.footer.set(footer ?? []);
    this.recommendations.set(responseData.recommendations);
  }

  public ngAfterViewInit(): void {
    this.adblockerActiveStatus();
  }

  private baseElementPageTypeSwitch(path: string): {
    page: string;
  } {
    switch (path) {
      case '':
        return { page: PAGE_TYPES.main_page };
      default:
        return { page: PAGE_TYPES.all_articles_and_sub_pages };
    }
  }

  private loadPortfolioData(): void {
    this.apiService
      .getPortfolioFooter()
      .pipe(take(1))
      .subscribe((data: PortfolioResponse) => {
        this.mediaworksFooter.set(data.data);
      });
  }

  isHomePage(): boolean {
    return this.documentElement?.location?.pathname === '/';
  }

  private adblockerActiveStatus(): boolean | void {
    if (!this.utils.isBrowser()) {
      //Manually override to return false, because the ado does not exist on SSR.
      return;
    }
    return this.isAdblockerActive.set(typeof this.window?.ado !== 'object');
  }

  openCookieSettings(): void {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }
}
