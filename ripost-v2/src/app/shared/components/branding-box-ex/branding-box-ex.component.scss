@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  position: relative;
  &.origo {
    background-color: var(--kui-black-100);
    font-family: var(--kui-font-secondary);
    border-bottom: 4px solid var(--kui-origo-blue);
    padding: 24px 16px 20px;
    .article {
      &-link {
        color: var(--kui-black);
        &:hover {
          color: var(--kui-origo-blue);
        }
      }
      &-title {
        font-size: 21px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: -0.21px;
      }
      &-list {
        display: flex;
        flex-direction: column;
        gap: 16px;
        padding-block: 24px 8px;
      }
    }
    .divider {
      width: 100%;
      height: 1px;
      background-color: var(--kui-gray-100);
      margin-top: 8px;
    }
    .logo {
      position: absolute;
      top: -15px;
    }
    .link {
      color: var(--kui-black);
      padding-top: 16px;
      font-size: 18px;
      font-weight: 500;
      line-height: 24px;
      letter-spacing: -0.18px;
      display: flex;
      gap: 4px;
      &:hover {
        color: var(--kui-origo-blue);
      }
      @include media-breakpoint-down(md) {
        font-size: 16px;
        letter-spacing: -0.16px;
      }
    }
    kesma-icon {
      color: var(--kui-origo-blue);
    }
  }
}
