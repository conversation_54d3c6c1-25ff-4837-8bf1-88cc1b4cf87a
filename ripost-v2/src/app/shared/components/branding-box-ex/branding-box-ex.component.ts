import { ChangeDetectionStrategy, Component, DestroyRef, effect, inject, input, signal } from '@angular/core';
import { BrandingBoxService } from '../../services/branding-box.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BrandingBoxArticle, IconComponent } from '@trendency/kesma-ui';
import { SlicePipe } from '@angular/common';

@Component({
  selector: 'app-branding-box-ex',
  templateUrl: './branding-box-ex.component.html',
  styleUrl: './branding-box-ex.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SlicePipe, IconComponent],
  host: { '[class]': 'brand()' },
})
export class BrandingBoxExComponent {
  private readonly brandingBoxService = inject(BrandingBoxService);
  private readonly destroyRef = inject(DestroyRef);

  brand = input.required<string>();
  articles = signal<Partial<BrandingBoxArticle>[]>([]);

  constructor() {
    effect(() => {
      const traffickingPlatform = this.getTraffickingPlatformByBrand();
      const utmSource = this.getUtmSourceByBrand();
      if (traffickingPlatform && utmSource) {
        this.brandingBoxService
          .getBrandingBoxData(traffickingPlatform, utmSource)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe((articles) => {
            this.articles.set(articles);
          });
      }
    });
  }

  private getTraffickingPlatformByBrand(): string | null {
    switch (this.brand()) {
      case 'origo':
        return 'Origo for Ripost';
    }
    return null;
  }

  private getUtmSourceByBrand(): string | null {
    switch (this.brand()) {
      case 'origo':
        return 'origo.hu';
    }
    return null;
  }
}
