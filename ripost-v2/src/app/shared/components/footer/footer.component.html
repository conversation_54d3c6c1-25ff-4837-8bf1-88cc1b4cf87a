<footer>
  <a class="logo" [routerLink]="'/'">
    <img class="logo-img" [src]="'/assets/images/logo.svg'" alt="Ripost logo" loading="lazy" />
  </a>
  <div class="menu-wrapper">
    <div class="socials">
      <a href="https://www.instagram.com/ripost.hu/" target="_blank">
        <kesma-icon [name]="'social-instagram-gray'" [size]="44"></kesma-icon>
      </a>
      <a href="https://www.facebook.com/ripost.hu" target="_blank">
        <kesma-icon [name]="'social-facebook-gray'" [size]="44"></kesma-icon>
      </a>
    </div>

    <div class="menu-items">
      <div class="menu-item-container">
        @for (menuItem of footerMenu(); track menuItem.title) {
          @if ((menuItem.children ?? []).length > 0) {
            <ng-container *ngTemplateOutlet="parentTemplate; context: { item: menuItem }"></ng-container>
          } @else {
            <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: menuItem }"></ng-container>
          }
        }
      </div>
      <div class="menu-item-container">
        <a class="menu-item" [routerLink]="'/szerzok'">Szerzők</a>
        <a class="menu-item" [routerLink]="'/impresszum'">Impresszum</a>
        <a class="menu-item" [routerLink]="'/adatvedelem'">Adatvédelmi tájékoztató</a>
        <a class="menu-item" [routerLink]="'/felhasznalasi-feltetelek'">Felhasználási feltételek</a>
        <a (click)="openCookieSettings.emit()" class="menu-item">Süti beállítások</a>
      </div>
    </div>

    <div class="copyright">© Ripost 2007–{{ currentYear }} Minden jog fenntartva.</div>
  </div>
</footer>

<ng-template #menuItemTemplate let-item="item">
  @if (item?.isCustomUrl) {
    <a class="menu-item" [href]="item.link" [target]="item.target">{{ item.title }}</a>
  } @else {
    <a class="menu-item" [routerLink]="item.link" [target]="item.target">{{ item.title }}</a>
  }
</ng-template>

<ng-template #childListTemplate let-childList="childList">
  <div class="child-list">
    @for (child of childList; track child.title) {
      <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: child }"></ng-container>
    }
  </div>
</ng-template>

<ng-template #parentTemplate let-item="item">
  <div class="menu-list">
    <ng-container *ngTemplateOutlet="menuItemTemplate; context: { item: item }"></ng-container>
    <ng-container *ngTemplateOutlet="childListTemplate; context: { childList: item.children ?? [] }"></ng-container>
  </div>
</ng-template>
