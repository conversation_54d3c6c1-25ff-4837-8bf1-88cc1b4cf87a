@use 'shared' as *;

:host {
  display: block;
}

footer {
  width: 100%;
  padding: 32px 30px;
  background-color: var(--kui-black-850);
  display: flex;
  align-items: center;
  margin-top: 24px;

  @include media-breakpoint-down(md) {
    flex-direction: column;
    padding: 32px 16px;
    margin-top: 0;
  }

  .logo {
    @include media-breakpoint-down(md) {
      margin: 64px 0;
    }
  }

  .menu-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;

    @include media-breakpoint-down(md) {
      align-items: center;
      gap: 32px;
    }

    .socials {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-right: 16px;
      gap: 24px;

      @include media-breakpoint-down(md) {
        justify-content: center;
        padding-right: 0;
      }
    }

    .menu-items {
      display: flex;
      flex-direction: column;

      .menu-item-container {
        display: flex;
        justify-content: flex-end;
        width: 100%;
        margin: 10px 0;
        flex-wrap: wrap;
        row-gap: 16px;

        @include media-breakpoint-down(md) {
          flex-direction: column;
          gap: 20px;
          justify-content: center;
          margin: 0;
          align-items: center;
        }

        .menu-list {
          display: flex;
          flex-direction: column;

          @include media-breakpoint-down(md) {
            align-items: center;
          }
        }

        .child-list {
          display: flex;
          flex-direction: column;

          @include media-breakpoint-down(md) {
            align-items: center;
          }
        }

        .menu-item {
          color: var(--kui-white);
          padding: 5px 16px;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 18.4px;
          cursor: pointer;
        }
      }
    }

    .copyright {
      color: var(--kui-white);
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 18.4px;
      padding-right: 16px;
      margin-left: auto;

      @include media-breakpoint-down(md) {
        margin: 0;
        padding: 0;
        text-align: center;
      }
    }
  }
}
