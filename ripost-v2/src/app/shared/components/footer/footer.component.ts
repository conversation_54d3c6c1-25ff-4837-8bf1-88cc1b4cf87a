import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';
import { IconComponent, SimplifiedMenuItem } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-footer',
  imports: [IconComponent, RouterLink, NgTemplateOutlet],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FooterComponent {
  footerMenu = input<SimplifiedMenuItem[]>();
  readonly openCookieSettings = output<void>();

  currentYear: number = new Date().getFullYear();
}
