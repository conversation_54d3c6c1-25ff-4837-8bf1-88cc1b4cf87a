@use 'shared' as *;

.block-big-image {
  background-size: cover;
  background-position: center;
  margin-top: 0;
  position: relative;

  @include media-breakpoint-down(md) {
    width: calc(100% + 30px);
    margin-top: 20px;
  }
}

.block-big-image {
  display: block;
  width: 100%;
  margin-bottom: 20px;
}

.no-margin {
  margin-left: -30px;
}

.thumbnail-info {
  display: none;
  flex-direction: column;
  bottom: 0;
  width: 100%;
  min-height: 58px;
  position: absolute;
  justify-content: center;
  background-color: var(--kui-black-100);
  padding-left: 20px;
  font-family: var(--kui-font-primary);
  letter-spacing: 0em;
  text-align: left;
  padding-top: 5px;
  padding-bottom: 5px;

  &:before {
    background-color: var(--kui-red-500);
  }

  .description {
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    @include media-breakpoint-down(sm) {
      font-size: 14px;
    }
  }

  .source {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    @include media-breakpoint-down(sm) {
      font-size: 12px;
    }
  }
}

.desktop {
  @include media-breakpoint-up(md) {
    display: flex;
  }
}

.mobile {
  @include media-breakpoint-down(md) {
    display: flex;
    position: relative;
    width: 100vw;
    margin-left: -15px;
    margin-top: -30px;
  }
}
