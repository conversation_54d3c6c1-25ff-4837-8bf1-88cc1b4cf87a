import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, ImageLayoutData } from '@trendency/kesma-ui';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-image',
  templateUrl: './image.component.html',
  styleUrl: './image.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass],
})
export class ImageComponent extends BaseComponent<ImageLayoutData> {}
