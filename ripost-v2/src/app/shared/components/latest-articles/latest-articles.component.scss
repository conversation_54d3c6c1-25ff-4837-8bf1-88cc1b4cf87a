@use 'shared' as *;

:host {
  font-family: var(--kui-font-secondary);
  position: relative;
  display: block;
  padding-top: 16px;

  .latest-articles-list {
    padding: 40px 8px;
    display: flex;
    flex-direction: column;
    gap: 32px;
    background-color: var(--kui-black-100);
    border-bottom: 4px solid var(--kui-red-500);

    @include media-breakpoint-down(md) {
      padding: 32px 10px;
      gap: 20px;
    }
  }

  .article-publish-time {
    clip-path: polygon(6% 0, 100% 0, 94% 100%, 0% 100%);
    padding: 5px;
    background-color: var(--kui-yellow-100);
    color: var(--kui-black);
    text-align: center;
    font-weight: 500;
    font-size: 16px;
    letter-spacing: -0.16px;
    line-height: 0;
    margin-bottom: 4px;
    font-style: italic;
    max-width: max-content;
    height: 24px;
    display: flex;
    align-items: center;

    @include media-breakpoint-down(md) {
      height: 20px;
    }
  }
}
