import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ReqService } from '@trendency/kesma-core';
import { ApiResult, ArticleSearchResult, backendDateToDate } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { searchResultToArticleCard } from '../../utils';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { ArticleCardType } from '../../definitions';
import { PublishedAgoPipe } from '../../pipes';
import { TitleBlockComponent } from '../title-block/title-block.component';

@Component({
  selector: 'app-latest-articles',
  imports: [ArticleCardComponent, PublishedAgoPipe, TitleBlockComponent],
  templateUrl: './latest-articles.component.html',
  styleUrl: './latest-articles.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LatestArticlesComponent {
  private readonly reqService = inject(ReqService);

  readonly articles = toSignal(
    this.reqService.get<ApiResult<(ArticleSearchResult & { recommendedTitle?: string })[]>>('/content-page/search', { params: { rowCount_limit: '5' } }).pipe(
      map(({ data }) =>
        data.map((article) => ({
          ...searchResultToArticleCard(article),
          title: article?.recommendedTitle || article?.title,
          publishDate: article.publishDate instanceof Date ? article.publishDate : backendDateToDate(article.publishDate as string),
        }))
      )
    )
  );
  protected readonly ArticleCardType = ArticleCardType;
}
