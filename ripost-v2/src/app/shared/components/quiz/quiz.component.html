<app-title-block [text]="'Kvíz'"></app-title-block>
@if (currentQuestionIndex() !== data?.questions?.length) {
  <div class="quiz-body">
    <div class="main-image">
      <img [src]="data?.questions?.[currentQuestionIndex()]?.image" alt="Kvíz képe" />
      <div class="question-index">{{ currentQuestionIndex() + 1 + '/' + (data?.questions?.length ?? +1) }}</div>
    </div>
    <div class="quiz-title">
      <h2>{{ data?.questions?.[currentQuestionIndex()]?.title }}</h2>
    </div>
    @if (currentQuestionIndex() !== data?.questions?.length) {
      <div class="quiz-answers">
        @for (answer of data?.questions?.[currentQuestionIndex()].answers; track answer.id; let answerIndex = $index) {
          <div
            class="answer"
            [class.wrong]="!answer.isCorrect && givenAnswers[currentQuestionIndex()] !== undefined && givenAnswers[currentQuestionIndex()] === answerIndex"
            [class.correct]="answer.isCorrect && givenAnswers[currentQuestionIndex()] !== undefined"
            [class.selected]="selectedAnswer()"
            (click)="
              givenAnswers[currentQuestionIndex()] !== undefined ? $event.stopPropagation() : onSelectAnswer(currentQuestionIndex(), answerIndex);
              onPickAnswer(answer)
            "
          >
            <input class="radio-input" type="radio" [name]="'answer_' + currentQuestion?.id" [id]="'answer_' + currentQuestion?.id + '_' + answerIndex" />
            <label class="radio-label" [for]="'answer_' + currentQuestion?.id + '_' + answerIndex">
              {{ answer.title }}
            </label>
          </div>
        }
        @if (isNextButtonVisible() && currentQuestionIndex() !== data?.questions?.length) {
          <app-simple-button [disabled]="!selectedAnswer()" (click)="onGetNextQuestion()">
            {{ currentQuestionIndex()! + 1 < data?.questions?.length! ? 'Következő kérdés' : 'Tovább az eredményre' }}</app-simple-button
          >
        }
      </div>
    }
  </div>
}

@if (rating && currentQuestionIndex() === data?.questions?.length) {
  <div class="quiz-body rating">
    <div class="main-image">
      <img [src]="rating?.image" alt="Kvíz képe" />
    </div>
    <div class="quiz-title rating">
      <h2>eredmény</h2>
    </div>
    <div class="quiz-rating">
      {{ rating.text }}
    </div>
  </div>
}
