@use 'shared' as *;
:host {
  position: relative;
  margin-top: 18px;

  @include media-breakpoint-down(md) {
    margin-top: 16px;
  }

  app-title-block ::ng-deep {
    .title-block {
      top: -18px;
      left: 30px;

      @include media-breakpoint-down(md) {
        top: -16px;
        left: 16px;
      }
    }
  }

  .main-image {
    position: relative;

    img {
      width: 100%;
    }

    .question-index {
      position: absolute;
      bottom: 16px;
      left: 16px;
      background-color: var(--kui-red-500);
      padding: 8px 15px;
      transform: skew(-12deg);
      color: var(--kui-white);
      text-align: center;
      font-family: var(--kui-font-secondary);
      font-size: 32px;
      font-weight: 500;
      line-height: 33.33px;
      letter-spacing: -0.32px;

      @include media-breakpoint-down(md) {
        font-size: 28px;
        line-height: 30px;
        letter-spacing: -0.28px;
      }
    }
  }

  .quiz {
    &-body {
      display: flex;
      flex-direction: column;
      background-color: var(--kui-yellow-100);
      padding: 52px 30px 24px 30px;
      gap: 24px;

      &.rating {
        gap: 0;
      }

      @include media-breakpoint-down(md) {
        padding: 40px 16px 24px 16px;
        gap: 16px;
      }
    }

    &-title {
      display: block;

      @include media-breakpoint-down(md) {
        margin-top: 8px;
      }

      &.rating {
        margin-bottom: 8px;
      }

      h2 {
        display: inline;
        width: fit-content;
        color: var(--kui-white);
        font-family: var(--kui-font-secondary);
        font-size: 38px;
        font-weight: 700;
        line-height: 51px;
        letter-spacing: -0.38px;
        text-transform: uppercase;
        background-color: var(--kui-black);
        padding: 0 8px;
        box-decoration-break: clone;
        -webkit-box-decoration-break: clone;

        @include media-breakpoint-down(md) {
          font-size: 26px;
          line-height: 31px;
          letter-spacing: -0.26px;
        }
      }
    }

    &-answers {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: -5px;

      .answer {
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
        letter-spacing: -0.16px;

        @include media-breakpoint-down(md) {
          font-size: 14px;
          line-height: 18px;
          letter-spacing: -0.14px;
        }

        .result-label {
          display: none;
          margin-left: auto;
          color: var(--kui-white);
          font-family: var(--kui-font-primary);
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 22px;
          text-align: right;

          @include media-breakpoint-down(md) {
            font-size: 14px;
            line-height: 18px;
          }
        }

        .radio-input {
          position: absolute;
          pointer-events: none;
          opacity: 0;
        }

        .radio-input:checked + label {
          &:after {
            opacity: 1;
            transform: scale(1);
          }
        }

        .radio-label {
          font-family: var(--kui-font-primary);
          font-size: 16px;
          font-weight: 500;
          line-height: 120%;
          font-style: normal;
          display: flex;
          align-items: center;
          cursor: pointer;

          &:before {
            display: inline-block;
            content: '';
            min-width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 16px;
            background-color: var(--kui-white);
          }

          &:after {
            content: '';
            position: absolute;
            display: inline-block;
            height: 8px;
            width: 8px;
            background-color: var(--kui-black);
            border-radius: 50%;
            transform: scale(0);
            opacity: 0;
            transition: 0.2s ease all;
            z-index: 1;
            left: 34px;

            @include media-breakpoint-down(md) {
              left: 20px;
            }
          }
        }

        &.correct {
          .radio-label {
            color: var(--kui-green-500);
          }
        }

        &.wrong {
          .radio-label {
            color: var(--kui-red-500);
          }
        }
      }
    }

    &-rating {
      font-size: 20px;
      line-height: 28px;
      letter-spacing: -0.2px;

      @include media-breakpoint-down(md) {
        font-size: 18px;
        line-height: 24px;
        letter-spacing: -0.18px;
      }
    }
  }
  app-simple-button {
    margin-top: 24px;

    @include media-breakpoint-down(md) {
      margin-top: 16px;
    }
  }
}
