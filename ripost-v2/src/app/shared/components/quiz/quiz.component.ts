import { ChangeDetectionStrategy, Component, computed, input, Signal, signal, WritableSignal } from '@angular/core';
import { QuizAnswer, QuizComponent as KesmaQuizComponent } from '@trendency/kesma-ui';
import { TitleBlockComponent } from '../title-block/title-block.component';
import { SimpleButtonComponent } from '../simple-button/simple-button.component';

@Component({
  selector: 'app-quiz',
  imports: [TitleBlockComponent, SimpleButtonComponent],
  templateUrl: './quiz.component.html',
  styleUrl: './quiz.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QuizComponent extends KesmaQuizComponent {
  desktopWidth = input<number>(12);
  currentQuestionIndex: WritableSignal<number> = signal<number>(0);
  selectedAnswer: WritableSignal<QuizAnswer | undefined> = signal<QuizAnswer | undefined>(undefined);

  readonly isNextButtonVisible: Signal<boolean> = computed(() => !!this.data?.questions?.length && this.data.questions.length > this.currentQuestionIndex());

  onPickAnswer(answer: QuizAnswer): void {
    this.selectedAnswer.set(answer);
  }

  onGetNextQuestion(): void {
    if ((this.data?.questions?.length && this.data.questions.length <= this.currentQuestionIndex()) || !this.selectedAnswer()) return;

    this.selectedAnswer.set(undefined);
    this.currentQuestionIndex.update((value) => value + 1);
    this.currentQuestion = this.data?.questions[this.currentQuestionIndex()];
  }
}
