@use 'shared' as *;
:host {
  button {
    span {
      font-family: var(--kui-font-secondary);
      text-align: center;
      font-size: 22px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      letter-spacing: -0.22px;
      text-transform: uppercase;

      @include media-breakpoint-down(md) {
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        line-height: 21.608px;
        letter-spacing: -0.18px;
      }
    }
  }
}
button.btn {
  &-primary {
    height: 50px;
    background-color: var(--kui-red-500);
    border-radius: 50px;
    padding: 12px 30px;

    &:hover {
      background-color: var(--kui-red-600);
    }
  }
}
