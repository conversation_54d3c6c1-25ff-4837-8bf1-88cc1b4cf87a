@use 'shared' as *;

.title-block {
  position: absolute;
  left: 16px;
  top: 0;
  height: 40px;
  max-width: max-content;
  display: flex;
  align-items: center;
  gap: 2px;

  .left-block {
    height: 40px;
    background-color: var(--kui-red-500);
    transform: skew(-12deg);
    width: 2px;

    @include media-breakpoint-down(md) {
      height: 30px;
    }
  }

  @include media-breakpoint-down(md) {
    height: 30px;
  }

  .title {
    background-color: var(--kui-red-500);
    display: flex;
    align-items: center;
    color: var(--kui-white);
    text-align: center;
    font-size: 32px;
    font-weight: 500;
    line-height: 33.333px;
    letter-spacing: -0.32px;
    text-transform: uppercase;
    padding: 0 9.6px;
    transform: skew(-12deg);
    height: 40px;

    @include media-breakpoint-down(md) {
      font-size: 24px;
      line-height: 25px;
      letter-spacing: -0.24px;
      height: 30px;
    }
  }
}
