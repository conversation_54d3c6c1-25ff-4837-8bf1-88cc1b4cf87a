@if (vm.state$ | async; as state) {
  <app-title-block [text]="'Szavazás'"></app-title-block>
  <div class="vote-body">
    <div class="vote-question">
      <h2>{{ data?.question }}</h2>
    </div>

    <ul class="vote-answer-list" [ngClass]="{ results: data?.isResultVisible || state.hasExpired }">
      @for (item of data?.answers; track item.id) {
        @if (!state.showResults) {
          <li>
            <div class="vote-answer-list-item" (click)="setVoteId(item.id)" [class.active]="voteId === item?.id">
              <input
                [id]="item.id"
                [name]="'answers' + data?.id"
                class="vote-answer-list-radio-button"
                type="radio"
                [value]="item.id"
                [checked]="voteId === item.id"
              />
              <label class="vote-answer-list-text" [for]="item.id"> {{ item.answer }} </label>
            </div>
          </li>
        } @else if (state.showResults) {
          <div class="vote-result">
            <div class="vote-result-text">{{ item.answer }}</div>
            <div class="vote-result-line">
              <div class="progress-bar">
                <div class="progress-bar-fill" [style.width]="item?.votePercentage + '%'"></div>
              </div>
              <span class="vote-result-percentage">{{ item?.votePercentage ?? '0' }}%</span>
            </div>
          </div>
        }
      }
    </ul>
    @if (!state.showResults) {
      <app-simple-button color="primary" [disabled]="!voteId" (click)="onVote()">Szavazok</app-simple-button>
    }

    <div class="vote-related-article">
      @if (!voteId || state.hasExpired) {
        <app-article-card
          [styleID]="ArticleCardType.TopImgColumnTitleLeadPadding"
          [data]="data?.article"
          [class.with-title-background]="true"
        ></app-article-card>
      } @else if (data?.evaluationArticle?.title) {
        <app-article-card
          [styleID]="ArticleCardType.TopImgColumnTitleLeadPadding"
          [data]="data?.evaluationArticle"
          [class.with-title-background]="true"
        ></app-article-card>
      }
    </div>
  </div>
}
