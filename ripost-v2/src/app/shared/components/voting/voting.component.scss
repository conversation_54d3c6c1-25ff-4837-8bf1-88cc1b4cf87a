@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  margin-top: 15px;
  position: relative;

  app-title-block ::ng-deep {
    position: absolute;
    left: 15px;
    top: -20px;
    .title-block {
      .title {
        text-wrap: nowrap;
      }
    }
  }

  .vote {
    &-body {
      padding: 40px 30px 24px 30px;
      background-color: var(--kui-yellow-100);
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    &-question {
      display: block;

      @include media-breakpoint-down(md) {
        margin-top: 8px;
      }

      h2 {
        display: inline;
        width: fit-content;
        color: var(--kui-white);
        font-family: var(--kui-font-secondary);
        font-size: 38px;
        font-weight: 700;
        line-height: 51px;
        letter-spacing: -0.38px;
        text-transform: uppercase;
        background-color: var(--kui-black);
        padding: 0 8px;
        box-decoration-break: clone;
        -webkit-box-decoration-break: clone;

        @include media-breakpoint-down(md) {
          font-size: 26px;
          line-height: 31px;
          letter-spacing: -0.26px;
        }
      }
    }

    &-answer-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding-right: 30px;

      &.results {
        padding-right: 0;
      }

      &-item {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      &-radio-button {
        appearance: none;
        margin: 0;
        width: 16px;
        min-width: 16px;
        height: 16px;
        border-radius: 50%;
        cursor: pointer;
        background: var(--kui-white);

        &::before {
          content: '';
          width: 12px;
          height: 12px;
          border-radius: 50%;
          transform: scale(0);
          transition: 150ms all ease-in-out;
        }

        &:checked {
          border: 3px solid var(--kui-white);
          background-color: var(--kui-red-500);
        }
      }

      &-text {
        font-size: 16px;
        line-height: 22px;
        cursor: pointer;
        letter-spacing: -0.16px;
      }
    }

    &-result {
      display: flex;
      flex-direction: column;
      gap: 8px;

      &-text {
        font-size: 16px;
        line-height: 22px;
        cursor: pointer;
        letter-spacing: -0.16px;
      }

      &-line {
        display: flex;
        width: 100%;
        align-items: center;
        gap: 24px;

        .progress-bar {
          height: 24px;
          background-color: var(--kui-yellow-200);
          border-radius: 4px;
          overflow: hidden;
          width: 100%;

          .progress-bar-fill {
            height: 100%;
            background-color: var(--kui-red-500);
            transition: width 0.2s ease-in-out;
          }
        }
      }

      &-percentage {
        font-size: 16px;
        line-height: 24px;
        flex-shrink: 0;
        width: 40px;
      }
    }

    &-related-article {
      app-article-card ::ng-deep {
        .article {
          &-data {
            background-color: var(--kui-black);
          }
          &-lead {
            color: var(--kui-white);
          }
        }
      }
    }
  }
}
