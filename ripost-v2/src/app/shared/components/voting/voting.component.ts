import { ChangeDetectionStrategy, Component } from '@angular/core';
import { VotingComponent as KesmaVotingComponent } from '@trendency/kesma-ui';
import { TitleBlockComponent } from '../title-block/title-block.component';
import { SimpleButtonComponent } from '../simple-button/simple-button.component';
import { AsyncPipe, NgClass } from '@angular/common';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { ArticleCardType } from '../../definitions';

@Component({
  selector: 'app-voting',
  templateUrl: './voting.component.html',
  styleUrl: './voting.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TitleBlockComponent, SimpleButtonComponent, NgClass, AsyncPipe, ArticleCardComponent],
})
export class VotingComponent extends KesmaVotingComponent {
  protected readonly ArticleCardType = ArticleCardType;
}
