@use 'shared' as *;

$box-shadow-size: 0px 0px 0px 0.5rem;

@mixin block-margin() {
  margin-bottom: 24px;
  @include media-breakpoint-down(sm) {
    margin-bottom: 16px;
  }
}

.block-content::ng-deep {
  @include block-margin;

  p,
  li,
  span {
    font-family: var(--kui-font-primary);
    font-size: 18px;
    line-height: 26px;
    font-weight: 400;

    @include media-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 24px;
      letter-spacing: -0.16px;
    }
  }

  h2 {
    font-size: 28px;
    font-family: var(--kui-font-secondary);
    line-height: 32px;
    font-weight: 600;
    margin-bottom: 24px;
    letter-spacing: -0.28px;
    @include media-breakpoint-down(sm) {
      font-size: 22px;
      line-height: 26px;
      letter-spacing: -0.22px;
      margin-bottom: 16px;
    }
  }

  p {
    @include block-margin;
  }

  a {
    color: var(--kui-red-500);
    font-weight: 600;
    text-decoration: underline;
  }

  strong,
  b {
    font-weight: 600;
  }

  i,
  em {
    font-style: italic;
  }

  figure.image {
    display: table;
    clear: both;
    text-align: center;
    margin-inline: auto;
    @include block-margin;

    &.image-style-align-left {
      float: left;
      margin-right: 20px;

      @include media-breakpoint-down(sm) {
        margin-right: 10px;
      }
    }

    &.image-style-align-right {
      float: right;
      margin-left: 20px;

      @include media-breakpoint-down(sm) {
        margin-left: 10px;
      }
    }

    figcaption {
      display: table-caption;
      caption-side: bottom;
      text-align: left;
      padding: 8px 16px 4px 16px;
      background-color: var(--kui-black-100);
      position: relative;
      font-size: 16px;
      line-height: 22px;
      border-bottom: 4px solid var(--kui-red-500);
      font-weight: 400;

      @include media-breakpoint-down(sm) {
        font-size: 14px;
        line-height: 16px;
      }

      &::before {
        content: none;
      }
    }
  }

  ul {
    margin: 30px 0;

    li {
      margin: 15px 0;
      padding-left: 40px;
      position: relative;

      &::marker {
        display: none;
      }

      &:before {
        position: absolute;
        height: 8px;
        width: 8px;
        border-radius: 50%;
        background: var(--kui-yellow-100);
        content: ' ';
        display: block;
        top: calc(50% - 4px);
        left: 16px;
      }
    }
  }

  ol {
    list-style-type: auto;
    padding-left: 40px;
    margin: 30px 0;

    li {
      margin: 15px 0;
      padding-left: 10px;
    }
  }

  ul > li {
    list-style: none;
  }

  .table-scroller {
    width: 100%;
    overflow: auto;
  }

  figure.table {
    max-width: 100%;
    overflow-x: auto;
    display: table;

    table tr,
    td {
      background-color: transparent;
      border: 1px solid var(--kui-red-500);
    }

    figcaption {
      background-color: var(--kui-gray-100);
      display: table-caption;
      caption-side: top;
      text-align: left;
      padding: 0.6em;
      font-size: 0.75em;
    }
  }

  table {
    max-width: 100%;
    border: 0;
    overflow-x: auto;
    border-spacing: 0;

    thead {
      tr {
        td,
        th {
          background: var(--kui-black);
          background: white;
          padding: 9px 16px;
          font-size: 16px;
          line-height: 26px;
          border: 1px solid var(--kui-red-500);
        }

        th {
          text-transform: uppercase;
        }

        @include media-breakpoint-down(md) {
          padding: 5px 16px;
        }
      }
    }

    tr {
      td,
      th {
        background: white;
        color: var(--kui-black);
        font-size: 16px;
        line-height: 26px;
        padding: 22px 16px;
        border-left: 1px solid var(--kui-red-500);
        border-top: 1px solid var(--kui-red-500);

        @include media-breakpoint-down(md) {
          padding: 10px 16px;
        }

        &:first-child {
          padding-left: 33px;
        }

        &:last-child {
          padding-right: 33px;
        }
      }

      th {
        font-weight: 500;
      }
    }

    td:last-of-type,
    th:last-of-type {
      border-right: 1px solid var(--kui-red-500);
    }

    tr:last-of-type {
      > td,
      > th {
        border-bottom: 1px solid var(--kui-red-500);
      }
    }
  }

  blockquote {
    margin: 30px 0;
    position: relative;

    @include media-breakpoint-down(sm) {
      margin: 30px 0 51px;
      padding: 20px 0 5px 20px;
    }

    p {
      &:last-child {
        margin-bottom: 0;
      }
    }

    &.quote {
      position: relative;
      z-index: 1;
      font-size: 30px;
      font-style: italic;
      letter-spacing: 0;
      line-height: 46px;
      font-family: var(--kui-font-secondary);
      color: var(--kui-black);
      @include media-breakpoint-down(sm) {
        font-size: 24px;
        line-height: 29px;
      }

      &,
      p {
        position: relative;
        z-index: 1;
        font-size: 48px;
        letter-spacing: 0;
        line-height: 52px;
        @include media-breakpoint-down(sm) {
          font-size: 32px;
          line-height: 36px;
        }
      }
    }

    &.highlight {
      margin-bottom: 30px;
      padding: 0 0.5rem;

      p {
        display: inline;
        padding: 0 0;
        font-size: 20px;
        line-height: 200%;
        background: var(--kui-yellow-100);
        box-shadow: $box-shadow-size var(--kui-yellow-100);
        @include media-breakpoint-down(sm) {
          font-size: 16px;
        }

        &.yellow {
          background: var(--kui-yellow-100);
          box-shadow: $box-shadow-size var(--kui-yellow-100);
        }

        &.red {
          background: var(--kui-red-500);
          color: var(--kui-white);
          box-shadow: $box-shadow-size var(--kui-red-500);
        }

        &.purple {
          background: var(--kui-purple-100);
          color: var(--kui-white);
          box-shadow: $box-shadow-size var(--kui-purple-100);
        }

        &.pink {
          background: var(--kui-pink-500);
          box-shadow: $box-shadow-size var(--kui-pink-500);
        }

        &.blue {
          background: var(--kui-blue-50);
          box-shadow: $box-shadow-size var(--kui-blue-50);
        }

        &.orange {
          background: var(--kui-orange-600);
          box-shadow: $box-shadow-size var(--kui-orange-600);
        }

        &.turquoise {
          background: var(--kui-green-50);
          box-shadow: $box-shadow-size var(--kui-green-50);
        }

        &.grey {
          background: var(--kui-gray-100);
          box-shadow: $box-shadow-size var(--kui-gray-100);
        }

        &.darkgreen {
          background: var(--kui-green-100);
          box-shadow: $box-shadow-size var(--kui-green-100);
        }

        &.darkblue {
          background: var(--kui-blue-300);
          color: var(--kui-white);
          box-shadow: $box-shadow-size var(--kui-blue-300);
        }
      }
    }

    &.border-text {
      border-top: 6px solid var(--kui-red-500);
      background: rgba(226, 0, 59, 0.1);
      padding: 30px;
    }
  }

  .custom-text-style {
    display: block;
    margin: 30px 0;
    position: relative;
    clear: both;

    &:last-child {
      margin-bottom: 0;
    }

    @include media-breakpoint-down(sm) {
      margin: 30px 0 51px;
      padding: 20px 0 5px 20px;
    }

    &.quote {
      position: relative;
      z-index: 1;
      font-size: 30px;
      font-style: italic;
      letter-spacing: 0;
      line-height: 46px;
      font-family: var(--kui-font-primary);
      border-left: none;
      padding: 15px 0 15px 30px;
      color: var(--kui-black);
      @include media-breakpoint-down(sm) {
        font-size: 24px;
        line-height: 29px;
      }

      &:before,
      &:after {
        display: flex;
        align-items: center;
        content: '';
        position: absolute;
        left: 30px;
        right: 30px;
        height: 22px;
        background-repeat: no-repeat;
      }

      &:before {
        top: 0;
        background-image: url('/assets/images/icons/quote.svg'), linear-gradient(to right, transparent 0, transparent 63px, var(--kui-yellow-100) 63px);
        padding-left: 30px;
        background-size:
          22px 22px,
          100% 2px;
        background-position:
          left center,
          left center;

        @include media-breakpoint-down(sm) {
          background-image: url('/assets/images/icons/quote.svg'), linear-gradient(to right, transparent 0, transparent 54px, var(--kui-yellow-100) 54px);
        }
      }

      &:after {
        bottom: 0;
        background-image: linear-gradient(to left, transparent 0, transparent 63px, var(--kui-yellow-100) 63px), url('/assets/images/icons/quote.svg');
        background-size:
          100% 2px,
          22px 22px;
        background-position:
          right center,
          right center;

        @include media-breakpoint-down(sm) {
          background-image: linear-gradient(to left, transparent 0, transparent 54px, var(--kui-yellow-100) 54px), url('/assets/images/icons/quote.svg');
        }
      }

      .quoteBlock-content {
        padding: 10px 0;
      }

      p {
        margin-bottom: 0;
      }

      .quoteBlock-content p:before,
      .quoteBlock-content p:after {
        content: none;
      }
    }

    &.highlight {
      padding: 0 24px 0 40px;
      color: var(--kui-black);
      border-left: 10px solid var(--kui-yellow-100);
      background-color: var(--kui-white);

      @include media-breakpoint-down(sm) {
        padding: 0 0 0 14px;
      }

      p {
        padding: 0 0;
        font-size: 18px;
        line-height: 26px;
        margin-bottom: 0;
        font-weight: 600;

        @include media-breakpoint-down(sm) {
          font-size: 16px;
          line-height: 22px;
          letter-spacing: -0.16;
        }

        &.yellow {
          background: var(--kui-yellow-100);
          box-shadow: $box-shadow-size var(--kui-yellow-100);
        }

        &.red {
          background: var(--kui-red-500);
          color: var(--kui-white);
          box-shadow: $box-shadow-size var(--kui-red-500);
        }

        &.purple {
          background: var(--kui-purple-100);
          color: var(--kui-white);
          box-shadow: var(--kui-purple-100);
        }

        &.pink {
          background: var(--kui-pink-500);
          box-shadow: $box-shadow-size var(--kui-pink-500);
        }

        &.blue {
          background: var(--kui-blue-50);
          box-shadow: $box-shadow-size var(--kui-blue-50);
        }

        &.orange {
          background: var(--kui-orange-600);
          box-shadow: $box-shadow-size var(--kui-orange-600);
        }

        &.turquoise {
          background: var(--kui-green-50);
          box-shadow: $box-shadow-size var(--kui-green-50);
        }

        &.grey {
          background: var(--kui-gray-100);
          box-shadow: $box-shadow-size var(--kui-gray-100);
        }

        &.darkgreen {
          background: var(--kui-green-100);
          box-shadow: $box-shadow-size var(--kui-green-100);
        }

        &.darkblue {
          background: var(--kui-blue-300);
          color: var(--kui-white);
          box-shadow: $box-shadow-size var(--kui-blue-300);
        }

        a {
          color: var(--kui-white);
        }
      }
    }

    &.border-text {
      border-top: 6px solid var(--kui-red-500);
      background: rgba(226, 0, 59, 0.1);
      padding: 16px 30px;

      p {
        margin-bottom: 0;
      }
    }

    &.underlined-text {
      font-weight: bold;
      font-size: 20px;
      margin: 40px 0;
      text-decoration: underline;
      text-decoration-color: var(--kui-red-500);
      text-decoration-thickness: 3px;
      word-break: break-word;
    }
  }

  .raw-html-embed {
    width: 100%;

    // Do not use flex here, because some 3rd party stuff (iframe.ly) doesn't like it
    display: block;

    > * {
      margin: 0 auto;
    }

    .instagram-media {
      margin: auto !important;
    }

    iframe {
      max-width: 100%;
      display: flex;
      justify-content: center;
    }
  }
}
