import { Pipe, PipeTransform } from '@angular/core';
import { differenceInHours, differenceInMinutes, format, isYesterday } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { hu } from 'date-fns/locale';

const ONE_MINUTE = 1;
const ONE_HOUR = 60;
const ONE_DAY = 24;

@Pipe({
  name: 'publishedAgo',
  standalone: true,
})
export class PublishedAgoPipe implements PipeTransform {
  transform(value: string | any, displayFormat = 'LLLL d.'): string {
    if (!value) {
      return '';
    }

    const now = new Date();

    let publishDate;
    if (value instanceof Date) {
      publishDate = value;
    } else if (value.date) {
      publishDate = new Date(`${value.date.replace(' ', 'T')}Z`);
    } else {
      publishDate = new Date(`${value.replace(' ', 'T')}Z`);
    }
    const timezoned = toZonedTime(publishDate, 'Europe/Budapest');

    const minutes = differenceInMinutes(now, timezoned);
    const hours = differenceInHours(now, timezoned);

    const digitAmount = timezoned.getMinutes() < 10 ? '0' : '';
    const isZeroMinute = minutes === 0;

    if (minutes >= 0 && minutes < ONE_HOUR) {
      // 1-59 perce
      return `${isZeroMinute ? ONE_MINUTE : minutes} perce`;
    }
    if (hours > 0 && hours < ONE_DAY) {
      // 1-23 órája
      return `${hours} órája`;
    }
    if (isYesterday(timezoned)) {
      // 'Tegnap' + óra perc
      return `Tegnap, ${timezoned.getHours()}:${digitAmount}${timezoned.getMinutes()}`;
    } else {
      return format(timezoned, displayFormat, { locale: hu });
    }
  }
}
