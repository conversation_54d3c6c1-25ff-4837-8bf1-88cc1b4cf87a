import { inject, Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  Advertisement,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleCategoryParams,
  ArticleSearchResult,
  BackendArticle,
  BackendArticleSearchResult,
  BasicDossier,
  buildMenuItem,
  DossierArticle,
  GalleryData,
  InitResponse,
  Layout,
  MenuTreeResponse,
  PortalBasedMenuLinkOverride,
  PortfolioResponse,
  SimplifiedMenuTree,
} from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  private readonly reqService = inject(ReqService);

  init(): Observable<ApiResult<InitResponse>> {
    return this.reqService.get('/init');
  }

  getMenu(overrides?: PortalBasedMenuLinkOverride): Observable<SimplifiedMenuTree> {
    return this.reqService.get<ApiResult<MenuTreeResponse>>('menu/tree').pipe(
      map(
        ({ data }) =>
          ({
            header: (data?.header ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_0: (data?.header_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_1: (data?.header_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer: (data?.footer ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_0: (data?.footer_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_1: (data?.footer_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
          }) as SimplifiedMenuTree
      )
    );
  }

  getAllCommercials(): Observable<ApiResult<Advertisement[], ApiResponseMetaList>> {
    return this.reqService.get('portal/commercials');
  }

  getLayoutPreview(hash: string): Observable<ApiResult<Layout>> {
    return this.reqService.get(`layout/preview/view?previewHash=${hash}`);
  }

  getCategoryLayout(categorySlug: string): Observable<ApiResult<Layout>> {
    return this.reqService.get<ApiResult<Layout>>(`column-layout/${categorySlug}`);
  }

  getCategoryArticles(
    categorySlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<BackendArticle[], ApiResponseMetaList>> {
    let params: ArticleCategoryParams = {
      columnSlug: categorySlug,
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      'excludedArticleIds[]': excludedIds ? excludedIds : [],
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;
    return this.reqService.get<ApiResult<BackendArticle[], ApiResponseMetaList>>(`content-page/articles-by-column`, { params: params }).pipe(
      map(({ data, meta }) => {
        return {
          meta,
          data: data.map((article) => {
            const [publishYear, publishMonth] = (article.publishDate as string).split('-');
            return {
              ...article,
              publishYear,
              publishMonth,
            };
          }),
        };
      })
    );
  }

  getSidebarArticleRecommendations(count: number, columnSlug?: string, excludedIds: string[] = []): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: count.toString(),
        'excludedArticleIds[]': excludedIds,
      },
    };
    params = columnSlug ? { ...params, columnSlug } : params;

    return this.reqService.get<ApiResult<ArticleCard[], ApiResponseMetaList>>(
      columnSlug ? '/content-page/articles-by-column?dev' : '/content-page/articles-by-last-day',
      { params }
    );
  }

  getOpinionAuthor(authorSlug: string, page = 0, itemsPerPage = 12): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-opinion-type`, {
      params: {
        author: authorSlug,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  getArticlesByTag(slug: string): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-tag?tagSlug=${slug}`);
  }

  getGalleries(page = 0, itemsPerPage = 12): Observable<ApiResult<GalleryData[], ApiResponseMetaList>> {
    return this.reqService.get(`/media/galleries`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  getDossier(dossierSlug: string, page = 0, itemsPerPage = 21): Observable<ApiResult<DossierArticle[], ApiResponseMetaList & Partial<BasicDossier>>> {
    return this.reqService.get(`/content-group/dossiers/${dossierSlug}`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  searchByKeyword(searchQuery: string, page?: number, rowCount_limit?: number): Observable<ApiResult<BackendArticleSearchResult[]>> {
    return this.reqService.get(`/content-page/search`, {
      params: { global_filter: searchQuery, rowCount_limit: rowCount_limit?.toString(), page_limit: page?.toString() },
    });
  }

  getPortfolioFooter(): Observable<PortfolioResponse> {
    return this.reqService.get('portal/portfolio-footer');
  }

  searchArticles(
    page: number,
    perPage = 10,
    filters: Record<string, string | string[] | undefined> | undefined
  ): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    const params = {
      rowCount_limit: perPage.toString(),
      page_limit: page.toString(),
      ...filters,
    };

    return this.reqService.get(`/content-page/search`, {
      params,
    });
  }
}
