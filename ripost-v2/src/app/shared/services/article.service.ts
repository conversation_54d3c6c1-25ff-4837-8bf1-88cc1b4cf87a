import { inject, Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import {
  ApiResult,
  Article,
  BackendArticle,
  BackendRecommendationsData,
  backendRecommendedArticleToArticleCard,
  externalRecommendationToArticleCard,
  RecommendationsData,
} from '@trendency/kesma-ui';
import { mapBackendArticleToArticle } from '../utils';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class ArticleService {
  private readonly reqService = inject(ReqService);

  getArticle(category: string, year: string, month: string, articleSlug: string): Observable<ApiResult<Article>> {
    return this.reqService.get<ApiResult<BackendArticle>>(`/content-page/article/${category}/${year}/${month.padStart(2, '0')}/${articleSlug}`).pipe(
      map(({ data, meta }: ApiResult<BackendArticle>) => ({
        data: mapBackendArticleToArticle(data),
        meta,
      }))
    );
  }

  getArticlePreview(articleSlug: string, previewHash: string, previewType: string): Observable<ApiResult<Article>> {
    const timestamp = Math.floor(new Date().getTime());
    const url = `/content-page/article/${articleSlug}/preview/view?previewHash=${previewHash}&t=${timestamp.toString()}`;
    return this.reqService
      .get<ApiResult<BackendArticle>>(url, {
        params: {
          previewType,
        },
      })
      .pipe(
        map(({ data, meta }) => ({
          data: mapBackendArticleToArticle(data),
          meta,
        }))
      );
  }

  getArticleRecommendations(articleSlug: string): Observable<ApiResult<RecommendationsData>> {
    return this.reqService.get<ApiResult<BackendRecommendationsData>>(`/content-page/article/${articleSlug}/recommendation`).pipe(
      map(({ data, meta }: ApiResult<BackendRecommendationsData>) => ({
        data: {
          ...data,
          highPriorityArticles: data.highPriorityArticles?.map(backendRecommendedArticleToArticleCard),
          lowPriorityArticles: data.lowPriorityArticles?.map(backendRecommendedArticleToArticleCard),
          externalRecommendation: data.externalRecommendation?.map(externalRecommendationToArticleCard),
        },
        meta,
      }))
    );
  }
}
