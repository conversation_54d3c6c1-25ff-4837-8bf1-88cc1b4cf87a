import { inject, Injectable } from '@angular/core';
import { CleanHttpService } from './clean-http.service';
import { EnvironmentApiUrl, UtilService } from '@trendency/kesma-core';
import { environment } from 'src/environments/environment';
import { catchError, map } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { BrandingBoxArticle, PersonalizedRecommendationApiResponse } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class BrandingBoxService {
  private readonly httpService = inject(CleanHttpService);
  private readonly utilsService = inject(UtilService);

  get deflectorApiUrl(): string {
    if (typeof environment.personalizedRecommendationApiUrl === 'string') {
      return environment.personalizedRecommendationApiUrl;
    }
    const { clientApiUrl, serverApiUrl } = environment.personalizedRecommendationApiUrl as EnvironmentApiUrl;
    return this.utilsService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  getBrandingBoxData(traffickingPlatforms: string, utmSource: string): Observable<Partial<BrandingBoxArticle>[]> {
    return this.httpService
      .get<PersonalizedRecommendationApiResponse>(`${this.deflectorApiUrl}/recommendation`, {
        params: {
          traffickingPlatforms,
          utmSource,
          withoutPos: '1',
        },
      })
      .pipe(
        map((data) => data[traffickingPlatforms]),
        catchError(() => {
          return of([]);
        })
      );
  }
}
