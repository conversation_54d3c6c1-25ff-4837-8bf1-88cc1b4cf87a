import { Injectable, signal } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UrlService {
  private currentUrl = '';
  private readonly previousUrl: BehaviorSubject<string> = new BehaviorSubject<string>('');

  readonly previousUrl$: Observable<string> = this.previousUrl.asObservable();

  navigationStartUrl = signal<string | null>(null);

  setPreviousUrl(newUrl: string): void {
    const previousUrl = this.previousUrl.value;
    this.previousUrl.next(this.currentUrl);
    if (!previousUrl && !newUrl.includes('/galeria/')) {
      this.currentUrl = newUrl;
    }
  }
}
