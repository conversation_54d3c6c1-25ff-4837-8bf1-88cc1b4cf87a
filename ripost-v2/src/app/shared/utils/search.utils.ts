import { ArticleCard, ArticleSearchResult, toBool } from '@trendency/kesma-ui';

export const searchResultToArticleCard = ({
  id,
  title,
  slug,
  columnTitle,
  columnSlug,
  publishDate,
  tag,
  lead,
  thumbnail: thumbnailUrl,
  author: authorName,
  year: publishYear,
  month: publishMonth,
  contentType,
  preTitle,
  tags,
  regions,
  likeCount,
  dislikeCount,
  commentCount,
  isLikesAndDislikesDisabled,
  isCommentsDisabled,
  isPaywalled,
  isVideo,
  isAdultsOnly,
  hasGallery,
  hasDossiers,
  hideThumbnailFromBody,
  foundationTagSlug,
  foundationTagTitle,
}: ArticleSearchResult): ArticleCard => ({
  id,
  title,
  preTitle,
  slug,
  category: {
    name: columnTitle,
    slug: columnSlug,
  },
  publishDate,
  publishYear,
  publishMonth,
  lead,
  thumbnail: {
    url: toBool(hideThumbnailFromBody) ? '' : (thumbnailUrl ?? ''),
  },
  author: {
    name: author<PERSON><PERSON>,
  },
  tags,
  regions,
  contentType,
  columnSlug,
  columnTitle,
  tag,
  likeCount,
  dislikeCount,
  commentCount,
  isLikesAndDislikesDisabled,
  isCommentsDisabled,
  isPaywalled: isPaywalled ? !!+isPaywalled : false,
  isVideoType: isVideo ? !!+isVideo : false,
  hasGallery: hasGallery ? !!+hasGallery : false,
  hasDossiers,
  isAdultsOnly,
  foundationTagSlug,
  foundationTagTitle,
});
