/**
 * @typedef {{
 *   tcString: string,
 *   tcfPolicyVersion: 2,
 *   cmpId: 1000,
 *   cmpVersion: 1000,
 *   gdprApplies: boolean | undefined,
 *   eventStatus: string,
 *   cmpStatus: string,
 *   listenerId: number | undefined,
 *   isServiceSpecific: boolean,
 *   useNonStandardStacks: boolean,
 *   publisherCC: string,
 *   purposeOneTreatment: boolean,
 *   purpose: {
 *     consents: {
 *       [purposeid]: boolean
 *     },
 *     legitimateInterests: {
 *       [purposeid]: boolean
 *     }
 *   },
 *   vendor: {
 *     consents: {
 *       [vendorid]: boolean
 *     },
 *     legitimateInterests: {
 *       [vendorid]: boolean
 *     }
 *   },
 *   specialFeatureOptins: {
 *     [specialfeatureid]: boolean
 *   },
 *   publisher: {
 *     consents: {
 *       [purposeid]: boolean
 *     },
 *     legitimateInterests: {
 *       [purposeid]: boolean
 *     },
 *     customPurpose: {
 *       consents: {
 *         [purposeid]: boolean
 *       },
 *       legitimateInterests: {
 *         [purposeid]: boolean
 *       },
 *     },
 *     restrictions: {
 *       [purposeid]: {
 *         [vendorid]: 1
 *       }
 *     }
 *   }
 * }} TCData
 */

class QuantCastHandler {
  static uspStubFunction (...args) {
    if(typeof window.__uspapi !== QuantCastHandler.uspStubFunction) {
      setTimeout(function() {
        if(typeof window.__uspapi !== 'undefined') {
          window.__uspapi.apply(window.__uspapi, args);
        }
      }, 500);
    }
  }

  /**
   * Initializes the `IFRAME` for the CMP, sets up event handler
   * and builds a stub to catch pre-init commands
   */
  static makeStub() {
    const TCF_LOCATOR_NAME = '__tcfapiLocator'
    const queue = []
    let win = window
    let cmpFrame

    /**
     * Looks for the top `Window` and if there is no existing embedder frame it creates one
     * @returns {boolean}
     */
    function addFrame() {
      const doc = win.document
      const otherCMP = !!(win.frames[ TCF_LOCATOR_NAME ])

      if(!otherCMP) {
        if(doc.body) {
          const iframe = doc.createElement('iframe')

          iframe.style.cssText = 'display:none'
          iframe.name = TCF_LOCATOR_NAME
          doc.body.appendChild(iframe)
        } else {
          setTimeout(addFrame, 5)
        }
      }
      return !otherCMP;
    }

    /**
     * TCF API Stub handler
     *
     * @param {string} command
     * @param {number} version
     * @param {function} [callback]
     * @param {any} [parameter]
     * @returns {*[]}
     */
    function tcfAPIHandler(command, version, callback, parameter) {
      let gdprApplies

      if(!command) {
        return queue
      }
      if (!callback) {
        callback = () => {}
      }

      switch (command) {
        case 'setGdprApplies':
          if(
            parameter !== undefined &&
            version === 2 &&
            typeof parameter === 'boolean'
          ) {
            gdprApplies = parameter;
            if(typeof callback === 'function') {
              callback('set', true);
            }
          }
          break
        case 'ping':
          const retr = {
            gdprApplies,
            cmpLoaded: false,
            cmpStatus: 'stub'
          }

          if(typeof callback === 'function') {
            callback(retr);
          }
          break;
        case 'init':
          if (typeof parameter === 'object') {
            parameter.tag_version = 'V2'
          }
        default:
          queue.push([command, version, callback, parameter]);
      }
    }

    /**
     * QuantCast event handler to communicate with the inframe
     * @param {MessageEvent} event
     */
    function postMessageEventHandler(event) {
      const msgIsString = typeof event.data === 'string'
      let json = {}

      try {
        json = msgIsString ? JSON.parse(event.data) : event.data
      } catch(ignore) { // possibly unknown message broadcasted by other iframes
        // console.warn('[QuantCast]: unprocessable event data received: ', event.data)
      }

      let payload = json.__tcfapiCall

      if(payload) {
        window.__tcfapi(
          payload.command,
          payload.version,
          function(retValue, success) {
            let returnMsg = {
              __tcfapiReturn: {
                returnValue: retValue,
                success: success,
                callId: payload.callId
              }
            }
            if(msgIsString) {
              returnMsg = JSON.stringify(returnMsg)
            }
            if(event && event.source && event.source.postMessage) {
              event.source.postMessage(returnMsg, '*')
            }
          },
          payload.parameter
        )
      }
    }

    while(win) {
      try {
        if(win.frames[ TCF_LOCATOR_NAME ]) {
          cmpFrame = win;
          break;
        }
      } catch(ignore) {}

      if(win === window.top) {
        break
      }
      win = win.parent
    }

    if(!cmpFrame) {
      addFrame()
      win.__tcfapi = tcfAPIHandler
      win.addEventListener('message', postMessageEventHandler, false)
    }
  }

  /**
   *
   * @param {string} quantCastId
   * @param {string} [host] defaults to `window.location.hostname`
   * @param {Function} [readyCallback] callback called after QuantCast init
   */
  static init(quantCastId, host, readyCallback) {
    host = host || window.location.hostname
    console.log('[QuantCast] initializing for domain: ', host)
    const element = document.createElement('script')
    const firstScript = document.getElementsByTagName('script')[0]
    const url = 'https://cmp.quantcast.com'.concat('/choice/', quantCastId, '/', host, '/choice.js?tag_version=V2')

    let uspTries = 0
    const uspTriesLimit = 12

    element.async = true
    element.type = 'text/javascript'
    element.src = url

    firstScript.parentNode.insertBefore(element, firstScript)

    this.makeStub() // needs to be added to catch calls before script load

    let uspInterval

    if(typeof window.__uspapi === 'undefined') {
      window.__uspapi = QuantCastHandler.uspStubFunction

      uspInterval = setInterval(() => {
          uspTries++;
          if(window.__uspapi === QuantCastHandler.uspStubFunction && uspTries < uspTriesLimit) {
            console.warn('[QuantCast]: unable to load USP')
            return
          }

          clearInterval(uspInterval)
        }, 6000
      )

      const callback =
        /**
         * @param {TCData} tcData
         * @param {boolean} success
         */
        (tcData, success) => {
        if(success && tcData.eventStatus === 'tcloaded') {
          __tcfapi('removeEventListener', 2, (success) => {
            if(success && typeof readyCallback === 'function') {
              readyCallback(tcData)
            }
          }, tcData.listenerId);
        } else {
          console.log(' [QuantCast::debug] tcfapi event: ', { tcData, success })
        }
      }

      __tcfapi('addEventListener', 2, callback)
    }
  }
}
