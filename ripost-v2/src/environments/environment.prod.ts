import { Environment } from '@trendency/kesma-core';

// <PERSON><PERSON>
export const environment: Environment = {
  production: true,
  type: 'prod',
  apiUrl: {
    clientApiUrl: 'https://ripost.hu/publicapi/hu',
    serverApiUrl: 'http://ripostfeapi.app.content.private/publicapi/hu',
  },
  personalizedRecommendationApiUrl: {
    clientApiUrl: 'https://terelo.mediaworks.hu/api',
    serverApiUrl: 'https://terelo.app.content.private/api',
  },
  facebookAppId: '1534911313439123',
  siteUrl: 'https://ripost.hu',
  googleSiteKey: '6Lc2WwceAAAAAI4hFFKpb9Kl5IxnwL2ItCW0l3H7',
  googleTagManager: 'GTM-PH2ZB6M',
  gemiusId: 'bIEw21Cek4Eqxm1OO6Ax93ZHrqxNss9oa.Pb8mcWUvb.i7',
  httpReqTimeout: 30, // second
};
