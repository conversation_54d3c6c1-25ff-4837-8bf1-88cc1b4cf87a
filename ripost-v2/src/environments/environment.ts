import { Environment } from '@trendency/kesma-core';

// <PERSON><PERSON><PERSON> fejlesztői környezet
export const environment: Environment = {
  production: false,
  type: 'local',
  apiUrl: 'https://kozponti-api.dev.trendency.hu/publicapi/hu', // for proxy: '/publicapi/hu' then: npm run start-with-proxy
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  facebookAppId: '1534911313439123',
  siteUrl: 'http://localhost:4200',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1',
  googleTagManager: 'GTM-PH2ZB6M',
  gemiusId: 'bIEw21Cek4Eqxm1OO6Ax93ZHrqxNss9oa.Pb8mcWUvb.i7',
  httpReqTimeout: 30, // second
};
