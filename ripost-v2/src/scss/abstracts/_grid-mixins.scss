@use 'variables' as *;
@use 'breakpoints' as *;

@mixin make-container($gutter: $grid-gutter-width) {
  width: 100%;
  padding-right: $gutter * 0.5;
  padding-left: $gutter * 0.5;
  margin-right: auto;
  margin-left: auto;
}

@mixin make-container-max-widths($max-widths: $container-max-widths, $breakpoints: $breakpoints) {
  @each $breakpoint, $container-max-width in $max-widths {
    @include media-breakpoint-up($breakpoint, $breakpoints) {
      max-width: $container-max-width;
    }
  }
}

@mixin make-row($gutter: $grid-gutter-width) {
  --bs-gutter-x: #{$gutter * 0.5};
  display: flex;
  flex-wrap: wrap;
  margin-right: $gutter * -0.5;
  margin-left: $gutter * -0.5;
}

@mixin make-col($size, $columns: $grid-columns) {
  flex: 0 0 percentage(calc($size / $columns));
  max-width: percentage(calc($size / $columns));
}

@mixin make-col-offset($size, $columns: $grid-columns) {
  $num: calc($size / $columns);
  margin-left: if($num==0, 0, percentage($num));
}

@mixin make-grid-columns($gutter: $grid-gutter-width) {
  %grid-column {
    position: relative;
    width: 100%;
    padding-right: $gutter * 0.5;
    padding-left: $gutter * 0.5;
  }

  @each $breakpoint in map-keys($breakpoints) {
    $infix: breakpoint-infix($breakpoint, $breakpoints);

    @for $i from 1 through $grid-columns {
      .col#{$infix}-#{$i} {
        @extend %grid-column;
      }
    }

    .col#{$infix},
    .col#{$infix}-auto {
      @extend %grid-column;
    }

    @include media-breakpoint-up($breakpoint, $breakpoints) {
      .col#{$infix} {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
      }

      .col#{$infix}-auto {
        flex: 0 0 auto;
        width: auto;
        max-width: 100%;
      }

      @for $i from 1 through $grid-columns {
        .col#{$infix}-#{$i} {
          @include make-col($i, $grid-columns);
        }
      }

      .order#{$infix}-first {
        order: -1;
      }

      .order#{$infix}-last {
        order: $grid-columns + 1;
      }

      @for $i from 0 through $grid-columns {
        .order#{$infix}-#{$i} {
          order: $i;
        }
      }

      @for $i from 0 through ($grid-columns - 1) {
        @if not($infix== '' and $i==0) {
          .offset#{$infix}-#{$i} {
            @include make-col-offset($i, $grid-columns);
          }
        }
      }
    }
  }
}
